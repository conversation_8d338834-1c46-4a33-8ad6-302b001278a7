using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Xna.Framework;
using Monogame.ECS.Core;
using Monogame.ECS.Core.BaseClasses;
using Monogame.Trigger.Core;
using Monogame.Trigger.Events;
using Monogame.Trigger.System;

namespace Monogame.Trigger.Integration
{
    /// <summary>
    /// Integration helper for adding event system support to the ECS World
    /// This shows how to integrate the event system with the existing MonoGame ECS architecture
    /// </summary>
    public class EventEnabledWorld
    {
        private readonly EntityManager _entityManager;
        private readonly EventSystem _eventSystem;
        private readonly List<ISystem> _systems;
        private readonly List<Type> _registeredSystemTypes;
        private readonly SystemEventSubscriptions _worldEventSubscriptions;

        public EntityManager EntityManager => _entityManager;
        public EventSystem EventSystem => _eventSystem;

        public EventEnabledWorld()
        {
            _entityManager = new EntityManager();
            _eventSystem = new EventSystem(maxQueueSize: 1000, maxEventsPerFrame: 50);
            _systems = new List<ISystem>();
            _registeredSystemTypes = new List<Type>();
            _worldEventSubscriptions = new SystemEventSubscriptions(_eventSystem);

            // Initialize the event system
            _eventSystem.Initialize();

            // Subscribe to world-level events
            SetupWorldEventSubscriptions();
        }

        private void SetupWorldEventSubscriptions()
        {
            // Subscribe to entity lifecycle events to keep track of entities
            _worldEventSubscriptions.Subscribe<EntityCreatedEvent>(OnEntityCreated);
            _worldEventSubscriptions.Subscribe<EntityDestroyedEvent>(OnEntityDestroyed);
            
            // Subscribe to system events
            _worldEventSubscriptions.Subscribe<ComponentAddedEvent>(OnComponentAdded);
            _worldEventSubscriptions.Subscribe<ComponentRemovedEvent>(OnComponentRemoved);
        }

        /// <summary>
        /// Add a system to the world with event system support
        /// </summary>
        public void AddSystem(ISystem system)
        {
            if (!_registeredSystemTypes.Contains(system.GetType()))
            {
                _systems.Add(system);
                _registeredSystemTypes.Add(system.GetType());
                system.Initialize();

                // If the system supports events, provide the event system
                if (system is IEventAwareSystem eventAwareSystem)
                {
                    eventAwareSystem.SetEventSystem(_eventSystem);
                }
            }
        }

        /// <summary>
        /// Add an entity and fire creation event
        /// </summary>
        public void AddEntity(Entity entity)
        {
            _entityManager.AddEntity(entity);

            // Fire entity created event
            var componentTypes = entity.GetAllComponents().Select(c => c.GetType()).ToArray();
            var createdEvent = ECSEventIntegration.CreateEntityCreatedEvent(
                entity.Id, entity.Name, componentTypes);
            _eventSystem.QueueEvent(createdEvent);
        }

        /// <summary>
        /// Remove an entity and fire destruction event
        /// </summary>
        public void RemoveEntity(Entity entity)
        {
            // Fire entity destroyed event before removal
            var destroyedEvent = ECSEventIntegration.CreateEntityDestroyedEvent(
                entity.Id, entity.Name);
            _eventSystem.QueueEvent(destroyedEvent);

            _entityManager.RemoveEntity(entity);
        }

        /// <summary>
        /// Update the world and process events
        /// </summary>
        public void Update(GameTime gameTime)
        {
            // Update entity manager
            _entityManager.Update();

            // Update all systems
            foreach (var system in _systems)
            {
                system.Update(gameTime);
            }

            // Process events after all systems have updated
            // This ensures events fired during system updates are processed
            _eventSystem.Update(gameTime);
        }

        /// <summary>
        /// Get entities with event system awareness
        /// </summary>
        public IEnumerable<Entity> GetEntities()
        {
            return _entityManager.GetEntities();
        }

        /// <summary>
        /// Get entities with specific components
        /// </summary>
        public IEnumerable<Entity> GetEntitiesWithComponent<T>() where T : Component
        {
            return _entityManager.GetEntitiesWithComponent<T>();
        }

        /// <summary>
        /// Get entities with multiple components
        /// </summary>
        public IEnumerable<Entity> GetEntitiesWithComponents(params Type[] componentTypes)
        {
            return _entityManager.GetEntitiesWithComponents(componentTypes);
        }

        /// <summary>
        /// Subscribe to events at the world level
        /// </summary>
        public IEventSubscription Subscribe<T>(Action<T> handler) where T : class, IEvent
        {
            return _worldEventSubscriptions.Subscribe(handler);
        }

        /// <summary>
        /// Queue an event at the world level
        /// </summary>
        public bool QueueEvent(IEvent eventToQueue)
        {
            return _worldEventSubscriptions.QueueEvent(eventToQueue);
        }

        /// <summary>
        /// Fire an event immediately at the world level
        /// </summary>
        public void FireEventImmediate(IEvent eventToFire)
        {
            _worldEventSubscriptions.FireEventImmediate(eventToFire);
        }

        // Event handlers for world-level events
        private void OnEntityCreated(EntityCreatedEvent eventData)
        {
            // World-level handling of entity creation
            // This could trigger auto-registration of systems, logging, etc.
            Console.WriteLine($"[World] Entity created: {eventData.EntityName} with {eventData.ComponentTypes.Length} components");
        }

        private void OnEntityDestroyed(EntityDestroyedEvent eventData)
        {
            // World-level handling of entity destruction
            Console.WriteLine($"[World] Entity destroyed: {eventData.EntityName}");
        }

        private void OnComponentAdded(ComponentAddedEvent eventData)
        {
            // World-level handling of component addition
            // This could trigger auto-registration of systems that need this component
            Console.WriteLine($"[World] Component {eventData.ComponentType.Name} added to entity {eventData.EntityId}");
        }

        private void OnComponentRemoved(ComponentRemovedEvent eventData)
        {
            // World-level handling of component removal
            Console.WriteLine($"[World] Component {eventData.ComponentType.Name} removed from entity {eventData.EntityId}");
        }

        /// <summary>
        /// Clean up the world and all its resources
        /// </summary>
        public void Dispose()
        {
            // Dispose of all event-aware systems
            foreach (var system in _systems)
            {
                if (system is IDisposable disposableSystem)
                {
                    disposableSystem.Dispose();
                }
            }

            // Dispose of world event subscriptions
            _worldEventSubscriptions?.Dispose();

            // Clear the event system
            _eventSystem?.Clear();
        }

        /// <summary>
        /// Get debug information about the world and event system
        /// </summary>
        public string GetDebugInfo()
        {
            var entityCount = _entityManager.GetEntities().Count();
            var systemCount = _systems.Count;
            var eventInfo = _eventSystem.GetDebugInfo();

            return $"World - Entities: {entityCount}, Systems: {systemCount}, {eventInfo}";
        }
    }

    /// <summary>
    /// Interface for systems that want to be aware of the event system
    /// </summary>
    public interface IEventAwareSystem
    {
        void SetEventSystem(EventSystem eventSystem);
    }

    /// <summary>
    /// Example of an event-aware system
    /// </summary>
    public class ExampleEventAwareSystem : ISystem, IEventAwareSystem, IDisposable
    {
        private readonly EntityManager _entityManager;
        private SystemEventSubscriptions _eventSubscriptions;

        public IEnumerable<Type> RequiredComponentTypes => new Type[0]; // No specific requirements

        public ExampleEventAwareSystem(EntityManager entityManager)
        {
            _entityManager = entityManager;
        }

        public void SetEventSystem(EventSystem eventSystem)
        {
            _eventSubscriptions = new SystemEventSubscriptions(eventSystem);
            
            // Subscribe to events this system cares about
            _eventSubscriptions.Subscribe<CollisionEnterEvent>(OnCollisionEnter);
            _eventSubscriptions.Subscribe<HealthChangedEvent>(OnHealthChanged);
        }

        public void Initialize()
        {
            // System initialization
        }

        public void Update(GameTime gameTime)
        {
            // System update logic
            
            // Example: Fire events when needed
            if (_eventSubscriptions != null)
            {
                // Example event firing
                var inputEvent = new InputActionEvent("SystemUpdate", null, gameTime.TotalGameTime);
                _eventSubscriptions.QueueEvent(inputEvent);
            }
        }

        private void OnCollisionEnter(CollisionEnterEvent eventData)
        {
            // Handle collision in this system
            Console.WriteLine($"[ExampleSystem] Collision detected between {eventData.EntityAId} and {eventData.EntityBId}");
        }

        private void OnHealthChanged(HealthChangedEvent eventData)
        {
            // Handle health change in this system
            Console.WriteLine($"[ExampleSystem] Health changed for entity {eventData.EntityId}: {eventData.HealthDelta}");
        }

        public void Dispose()
        {
            _eventSubscriptions?.Dispose();
        }
    }
}
