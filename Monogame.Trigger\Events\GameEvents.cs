using System;
using Microsoft.Xna.Framework;
using Monogame.Trigger.Core;

namespace Monogame.Trigger.Events
{
    // Entity-related events
    public class EntityCreatedEvent : BaseEvent
    {
        public Guid EntityId { get; }
        public string EntityName { get; }
        public Type[] ComponentTypes { get; }

        public EntityCreatedEvent(Guid entityId, string entityName, Type[] componentTypes, string source = "EntityManager")
            : base(priority: 1, source: source)
        {
            EntityId = entityId;
            EntityName = entityName;
            ComponentTypes = componentTypes ?? new Type[0];
        }
    }

    public class EntityDestroyedEvent : BaseEvent
    {
        public Guid EntityId { get; }
        public string EntityName { get; }

        public EntityDestroyedEvent(Guid entityId, string entityName, string source = "EntityManager")
            : base(priority: 1, source: source)
        {
            EntityId = entityId;
            EntityName = entityName;
        }
    }

    public class ComponentAddedEvent : BaseEvent
    {
        public Guid EntityId { get; }
        public Type ComponentType { get; }
        public object Component { get; }

        public ComponentAddedEvent(Guid entityId, Type componentType, object component, string source = "Entity")
            : base(priority: 0, source: source)
        {
            EntityId = entityId;
            ComponentType = componentType;
            Component = component;
        }
    }

    public class ComponentRemovedEvent : BaseEvent
    {
        public Guid EntityId { get; }
        public Type ComponentType { get; }

        public ComponentRemovedEvent(Guid entityId, Type componentType, string source = "Entity")
            : base(priority: 0, source: source)
        {
            EntityId = entityId;
            ComponentType = componentType;
        }
    }

    // Collision events
    public class CollisionEnterEvent : BaseEvent
    {
        public Guid EntityAId { get; }
        public Guid EntityBId { get; }
        public Vector2 CollisionPoint { get; }
        public Rectangle IntersectionArea { get; }
        public string CollisionTypeA { get; }
        public string CollisionTypeB { get; }

        public CollisionEnterEvent(Guid entityAId, Guid entityBId, Vector2 collisionPoint, 
            Rectangle intersectionArea, string collisionTypeA, string collisionTypeB)
            : base(priority: 2, source: "CollisionSystem")
        {
            EntityAId = entityAId;
            EntityBId = entityBId;
            CollisionPoint = collisionPoint;
            IntersectionArea = intersectionArea;
            CollisionTypeA = collisionTypeA;
            CollisionTypeB = collisionTypeB;
        }
    }

    public class CollisionExitEvent : BaseEvent
    {
        public Guid EntityAId { get; }
        public Guid EntityBId { get; }

        public CollisionExitEvent(Guid entityAId, Guid entityBId)
            : base(priority: 1, source: "CollisionSystem")
        {
            EntityAId = entityAId;
            EntityBId = entityBId;
        }
    }

    // Health and combat events
    public class HealthChangedEvent : BaseEvent
    {
        public Guid EntityId { get; }
        public int PreviousHealth { get; }
        public int CurrentHealth { get; }
        public int MaxHealth { get; }
        public int HealthDelta { get; }
        public string ChangeReason { get; }

        public HealthChangedEvent(Guid entityId, int previousHealth, int currentHealth, 
            int maxHealth, string changeReason = "")
            : base(priority: 1, source: "HealthComponent")
        {
            EntityId = entityId;
            PreviousHealth = previousHealth;
            CurrentHealth = currentHealth;
            MaxHealth = maxHealth;
            HealthDelta = currentHealth - previousHealth;
            ChangeReason = changeReason;
        }
    }

    public class EntityDeathEvent : BaseEvent
    {
        public Guid EntityId { get; }
        public string EntityName { get; }
        public Guid? KillerId { get; }
        public string DeathCause { get; }

        public EntityDeathEvent(Guid entityId, string entityName, Guid? killerId = null, string deathCause = "")
            : base(priority: 3, source: "HealthComponent")
        {
            EntityId = entityId;
            EntityName = entityName;
            KillerId = killerId;
            DeathCause = deathCause;
        }
    }

    public class DamageDealtEvent : BaseEvent
    {
        public Guid AttackerId { get; }
        public Guid TargetId { get; }
        public int DamageAmount { get; }
        public string DamageType { get; }
        public bool IsCritical { get; }

        public DamageDealtEvent(Guid attackerId, Guid targetId, int damageAmount, 
            string damageType = "Physical", bool isCritical = false)
            : base(priority: 2, source: "CombatSystem")
        {
            AttackerId = attackerId;
            TargetId = targetId;
            DamageAmount = damageAmount;
            DamageType = damageType;
            IsCritical = isCritical;
        }
    }

    public class HealingReceivedEvent : BaseEvent
    {
        public Guid HealerId { get; }
        public Guid TargetId { get; }
        public int HealingAmount { get; }
        public string HealingSource { get; }

        public HealingReceivedEvent(Guid healerId, Guid targetId, int healingAmount, string healingSource = "")
            : base(priority: 1, source: "HealingSystem")
        {
            HealerId = healerId;
            TargetId = targetId;
            HealingAmount = healingAmount;
            HealingSource = healingSource;
        }
    }

    // Turn-based events
    public class TurnStartedEvent : BaseEvent
    {
        public Guid EntityId { get; }
        public int TurnNumber { get; }
        public int RoundNumber { get; }

        public TurnStartedEvent(Guid entityId, int turnNumber, int roundNumber)
            : base(priority: 2, source: "TurnBasedSystem")
        {
            EntityId = entityId;
            TurnNumber = turnNumber;
            RoundNumber = roundNumber;
        }
    }

    public class TurnEndedEvent : BaseEvent
    {
        public Guid EntityId { get; }
        public int ActionsUsed { get; }
        public bool TurnSkipped { get; }

        public TurnEndedEvent(Guid entityId, int actionsUsed, bool turnSkipped = false)
            : base(priority: 2, source: "TurnBasedSystem")
        {
            EntityId = entityId;
            ActionsUsed = actionsUsed;
            TurnSkipped = turnSkipped;
        }
    }

    public class RoundStartedEvent : BaseEvent
    {
        public int RoundNumber { get; }
        public Guid[] ParticipantIds { get; }

        public RoundStartedEvent(int roundNumber, Guid[] participantIds)
            : base(priority: 3, source: "TurnBasedSystem")
        {
            RoundNumber = roundNumber;
            ParticipantIds = participantIds ?? new Guid[0];
        }
    }

    public class RoundEndedEvent : BaseEvent
    {
        public int RoundNumber { get; }
        public int TotalTurns { get; }

        public RoundEndedEvent(int roundNumber, int totalTurns)
            : base(priority: 3, source: "TurnBasedSystem")
        {
            RoundNumber = roundNumber;
            TotalTurns = totalTurns;
        }
    }

    // Ability and action events
    public class AbilityUsedEvent : BaseEvent
    {
        public Guid CasterId { get; }
        public string AbilityId { get; }
        public string AbilityName { get; }
        public Guid[] TargetIds { get; }
        public int ManaCost { get; }
        public bool WasSuccessful { get; }

        public AbilityUsedEvent(Guid casterId, string abilityId, string abilityName, 
            Guid[] targetIds, int manaCost, bool wasSuccessful = true)
            : base(priority: 2, source: "AbilitySystem")
        {
            CasterId = casterId;
            AbilityId = abilityId;
            AbilityName = abilityName;
            TargetIds = targetIds ?? new Guid[0];
            ManaCost = manaCost;
            WasSuccessful = wasSuccessful;
        }
    }

    public class ManaChangedEvent : BaseEvent
    {
        public Guid EntityId { get; }
        public int PreviousMana { get; }
        public int CurrentMana { get; }
        public int MaxMana { get; }
        public string ChangeReason { get; }

        public ManaChangedEvent(Guid entityId, int previousMana, int currentMana, 
            int maxMana, string changeReason = "")
            : base(priority: 0, source: "AbilityComponent")
        {
            EntityId = entityId;
            PreviousMana = previousMana;
            CurrentMana = currentMana;
            MaxMana = maxMana;
            ChangeReason = changeReason;
        }
    }

    // Movement events
    public class EntityMovedEvent : BaseEvent
    {
        public Guid EntityId { get; }
        public Vector2 PreviousPosition { get; }
        public Vector2 NewPosition { get; }
        public Vector2 Velocity { get; }

        public EntityMovedEvent(Guid entityId, Vector2 previousPosition, Vector2 newPosition, Vector2 velocity)
            : base(priority: 0, source: "MovementSystem")
        {
            EntityId = entityId;
            PreviousPosition = previousPosition;
            NewPosition = newPosition;
            Velocity = velocity;
        }
    }

    // Input events
    public class InputActionEvent : BaseEvent
    {
        public string ActionName { get; }
        public Guid? PlayerId { get; }
        public object ActionData { get; }

        public InputActionEvent(string actionName, Guid? playerId = null, object actionData = null)
            : base(priority: 1, isImmediate: true, source: "InputSystem")
        {
            ActionName = actionName;
            PlayerId = playerId;
            ActionData = actionData;
        }
    }
}
