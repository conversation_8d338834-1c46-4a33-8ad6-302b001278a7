using System;
using System.Collections.Generic;
using Microsoft.Xna.Framework;
using Monogame.ECS.Core.BaseClasses;

namespace Monogame.ECS.Components
{
    public enum AIBehaviorType
    {
        Passive,
        Aggressive,
        Defensive,
        Patrol,
        Guard,
        Flee,
        Follow
    }

    public enum AIState
    {
        Idle,
        Patrolling,
        Chasing,
        Attacking,
        Fleeing,
        Returning,
        Dead
    }

    public class AIComponent : Component
    {
        public AIBehaviorType BehaviorType { get; set; }
        public AIState CurrentState { get; set; }
        public float DetectionRange { get; set; }
        public float AttackRange { get; set; }
        public float PatrolRadius { get; set; }
        public Vector2 HomePosition { get; set; }
        public List<Vector2> PatrolPoints { get; set; }
        public int CurrentPatrolIndex { get; set; }
        public Guid? TargetEntityId { get; set; }
        public float StateTimer { get; set; }
        public float DecisionCooldown { get; set; }
        public bool IsEnabled { get; set; }

        public AIComponent()
        {
            BehaviorType = AIBehaviorType.Passive;
            CurrentState = AIState.Idle;
            DetectionRange = 100f;
            AttackRange = 50f;
            PatrolRadius = 150f;
            HomePosition = Vector2.Zero;
            PatrolPoints = new List<Vector2>();
            CurrentPatrolIndex = 0;
            TargetEntityId = null;
            StateTimer = 0f;
            DecisionCooldown = 1f; // 1 second between decisions
            IsEnabled = true;
        }

        public AIComponent(AIBehaviorType behaviorType, Vector2 homePosition) : this()
        {
            BehaviorType = behaviorType;
            HomePosition = homePosition;
        }

        public void SetTarget(Guid? targetId)
        {
            TargetEntityId = targetId;
        }

        public void ClearTarget()
        {
            TargetEntityId = null;
        }

        public void SetState(AIState newState)
        {
            CurrentState = newState;
            StateTimer = 0f;
        }

        public void AddPatrolPoint(Vector2 point)
        {
            PatrolPoints.Add(point);
        }

        public Vector2? GetCurrentPatrolPoint()
        {
            if (PatrolPoints.Count == 0)
                return null;

            return PatrolPoints[CurrentPatrolIndex % PatrolPoints.Count];
        }

        public void NextPatrolPoint()
        {
            if (PatrolPoints.Count > 0)
            {
                CurrentPatrolIndex = (CurrentPatrolIndex + 1) % PatrolPoints.Count;
            }
        }

        public void UpdateTimers(GameTime gameTime)
        {
            StateTimer += (float)gameTime.ElapsedGameTime.TotalSeconds;
            DecisionCooldown -= (float)gameTime.ElapsedGameTime.TotalSeconds;
        }

        public bool CanMakeDecision()
        {
            return IsEnabled && DecisionCooldown <= 0f;
        }

        public void ResetDecisionCooldown(float cooldown = 1f)
        {
            DecisionCooldown = cooldown;
        }
    }
}
