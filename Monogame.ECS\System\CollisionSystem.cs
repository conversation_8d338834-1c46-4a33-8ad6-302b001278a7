using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Xna.Framework;
using Monogame.ECS.Core;
using Monogame.ECS.Core.BaseClasses;
using Monogame.ECS.Components;

namespace Monogame.ECS.System
{
    public struct CollisionInfo
    {
        public Entity EntityA { get; set; }
        public Entity EntityB { get; set; }
        public CollisionType TypeA { get; set; }
        public CollisionType TypeB { get; set; }
        public Rectangle IntersectionArea { get; set; }
    }

    public class CollisionSystem : ISystem
    {
        private readonly EntityManager _entityManager;
        private readonly List<CollisionInfo> _currentCollisions;
        private readonly List<CollisionInfo> _previousCollisions;

        public IEnumerable<Type> RequiredComponentTypes => new[]
        {
            typeof(TransformComponent),
            typeof(CollisionComponent)
        };

        // Events for collision handling
        public event Action<CollisionInfo> OnCollisionEnter;
        public event Action<CollisionInfo> OnCollisionStay;
        public event Action<CollisionInfo> OnCollisionExit;

        public CollisionSystem(EntityManager entityManager)
        {
            _entityManager = entityManager;
            _currentCollisions = new List<CollisionInfo>();
            _previousCollisions = new List<CollisionInfo>();
        }

        public void Initialize()
        {
            // No initialization needed
        }

        public void Update(GameTime gameTime)
        {
            // Move current collisions to previous
            _previousCollisions.Clear();
            _previousCollisions.AddRange(_currentCollisions);
            _currentCollisions.Clear();

            var collidableEntities = _entityManager.GetEntitiesWithComponents(
                typeof(TransformComponent),
                typeof(CollisionComponent))
                .Where(e => e.GetComponent<CollisionComponent>().IsEnabled)
                .ToList();

            // Check all pairs of entities for collisions
            for (int i = 0; i < collidableEntities.Count; i++)
            {
                for (int j = i + 1; j < collidableEntities.Count; j++)
                {
                    var entityA = collidableEntities[i];
                    var entityB = collidableEntities[j];

                    CheckCollision(entityA, entityB);
                }
            }

            // Fire collision events
            ProcessCollisionEvents();
        }

        private void CheckCollision(Entity entityA, Entity entityB)
        {
            var transformA = entityA.GetComponent<TransformComponent>();
            var transformB = entityB.GetComponent<TransformComponent>();
            var collisionA = entityA.GetComponent<CollisionComponent>();
            var collisionB = entityB.GetComponent<CollisionComponent>();

            if (collisionA.Intersects(collisionB, transformA.Position, transformB.Position))
            {
                var boundsA = collisionA.GetWorldBounds(transformA.Position);
                var boundsB = collisionB.GetWorldBounds(transformB.Position);
                var intersection = Rectangle.Intersect(boundsA, boundsB);

                var collisionInfo = new CollisionInfo
                {
                    EntityA = entityA,
                    EntityB = entityB,
                    TypeA = collisionA.Type,
                    TypeB = collisionB.Type,
                    IntersectionArea = intersection
                };

                _currentCollisions.Add(collisionInfo);
            }
        }

        private void ProcessCollisionEvents()
        {
            // Find new collisions (enter events)
            foreach (var current in _currentCollisions)
            {
                bool wasColliding = _previousCollisions.Any(prev => 
                    AreEntitiesEqual(prev, current));

                if (!wasColliding)
                {
                    OnCollisionEnter?.Invoke(current);
                    HandleCollisionEnter(current);
                }
                else
                {
                    OnCollisionStay?.Invoke(current);
                    HandleCollisionStay(current);
                }
            }

            // Find ended collisions (exit events)
            foreach (var previous in _previousCollisions)
            {
                bool stillColliding = _currentCollisions.Any(current => 
                    AreEntitiesEqual(previous, current));

                if (!stillColliding)
                {
                    OnCollisionExit?.Invoke(previous);
                    HandleCollisionExit(previous);
                }
            }
        }

        private bool AreEntitiesEqual(CollisionInfo a, CollisionInfo b)
        {
            return (a.EntityA.Id == b.EntityA.Id && a.EntityB.Id == b.EntityB.Id) ||
                   (a.EntityA.Id == b.EntityB.Id && a.EntityB.Id == b.EntityA.Id);
        }

        private void HandleCollisionEnter(CollisionInfo collision)
        {
            // Handle specific collision types
            if (collision.TypeA == CollisionType.Player && collision.TypeB == CollisionType.Item)
            {
                HandleItemPickup(collision.EntityA, collision.EntityB);
            }
            else if (collision.TypeA == CollisionType.Item && collision.TypeB == CollisionType.Player)
            {
                HandleItemPickup(collision.EntityB, collision.EntityA);
            }
            else if (collision.TypeA == CollisionType.Player && collision.TypeB == CollisionType.Enemy)
            {
                HandlePlayerEnemyCollision(collision.EntityA, collision.EntityB);
            }
            else if (collision.TypeA == CollisionType.Enemy && collision.TypeB == CollisionType.Player)
            {
                HandlePlayerEnemyCollision(collision.EntityB, collision.EntityA);
            }
        }

        private void HandleCollisionStay(CollisionInfo collision)
        {
            // Handle ongoing collisions
            // For example, damage over time effects
        }

        private void HandleCollisionExit(CollisionInfo collision)
        {
            // Handle collision exit events
            // For example, leaving a trigger area
        }

        private void HandleItemPickup(Entity player, Entity item)
        {
            var inventory = player.GetComponent<InventoryComponent>();
            if (inventory != null)
            {
                // Create an inventory item from the entity
                // This is a simplified example - you'd want more sophisticated item data
                var itemName = item.Name ?? "Unknown Item";
                var inventoryItem = new InventoryItem("item_" + item.Id, itemName, 1);
                
                if (inventory.AddItem(inventoryItem))
                {
                    // Successfully picked up item, remove from world
                    _entityManager.RemoveEntity(item);
                }
            }
        }

        private void HandlePlayerEnemyCollision(Entity player, Entity enemy)
        {
            // Handle player touching enemy
            // In a turn-based game, this might initiate combat
            var playerHealth = player.GetComponent<HealthComponent>();
            var enemyStats = enemy.GetComponent<StatsComponent>();

            if (playerHealth != null && enemyStats != null)
            {
                // Simple damage on touch (you might want more sophisticated combat)
                playerHealth.TakeDamage(enemyStats.AttackPower / 4); // Reduced damage for touch
            }
        }

        public bool IsEntityCollidingWith(Entity entity, CollisionType targetType)
        {
            return _currentCollisions.Any(collision =>
                (collision.EntityA.Id == entity.Id && collision.TypeB == targetType) ||
                (collision.EntityB.Id == entity.Id && collision.TypeA == targetType));
        }

        public List<Entity> GetCollidingEntities(Entity entity)
        {
            var collidingEntities = new List<Entity>();

            foreach (var collision in _currentCollisions)
            {
                if (collision.EntityA.Id == entity.Id)
                    collidingEntities.Add(collision.EntityB);
                else if (collision.EntityB.Id == entity.Id)
                    collidingEntities.Add(collision.EntityA);
            }

            return collidingEntities;
        }
    }
}
