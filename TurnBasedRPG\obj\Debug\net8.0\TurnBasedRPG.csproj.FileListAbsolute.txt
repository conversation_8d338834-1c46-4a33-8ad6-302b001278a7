C:\Projects\MonoGame\TurnBasedRPG\TurnBased_20250822\TurnBasedRPG\obj\Debug\net8.0\TurnBasedRPG.csproj.AssemblyReference.cache
C:\Projects\MonoGame\TurnBasedRPG\TurnBased_20250822\TurnBasedRPG\obj\Debug\net8.0\TurnBasedRPG.GeneratedMSBuildEditorConfig.editorconfig
C:\Projects\MonoGame\TurnBasedRPG\TurnBased_20250822\TurnBasedRPG\obj\Debug\net8.0\TurnBasedRPG.AssemblyInfoInputs.cache
C:\Projects\MonoGame\TurnBasedRPG\TurnBased_20250822\TurnBasedRPG\obj\Debug\net8.0\TurnBasedRPG.AssemblyInfo.cs
C:\Projects\MonoGame\TurnBasedRPG\TurnBased_20250822\TurnBasedRPG\obj\Debug\net8.0\TurnBasedRPG.csproj.CoreCompileInputs.cache
C:\Projects\MonoGame\TurnBasedRPG\TurnBased_20250822\TurnBasedRPG\bin\Debug\net8.0\TurnBasedRPG.exe
C:\Projects\MonoGame\TurnBasedRPG\TurnBased_20250822\TurnBasedRPG\bin\Debug\net8.0\TurnBasedRPG.deps.json
C:\Projects\MonoGame\TurnBasedRPG\TurnBased_20250822\TurnBasedRPG\bin\Debug\net8.0\TurnBasedRPG.runtimeconfig.json
C:\Projects\MonoGame\TurnBasedRPG\TurnBased_20250822\TurnBasedRPG\bin\Debug\net8.0\TurnBasedRPG.dll
C:\Projects\MonoGame\TurnBasedRPG\TurnBased_20250822\TurnBasedRPG\bin\Debug\net8.0\TurnBasedRPG.pdb
C:\Projects\MonoGame\TurnBasedRPG\TurnBased_20250822\TurnBasedRPG\bin\Debug\net8.0\MonoGame.Framework.dll
C:\Projects\MonoGame\TurnBasedRPG\TurnBased_20250822\TurnBasedRPG\bin\Debug\net8.0\NVorbis.dll
C:\Projects\MonoGame\TurnBasedRPG\TurnBased_20250822\TurnBasedRPG\bin\Debug\net8.0\runtimes\linux-x64\native\libopenal.so.1
C:\Projects\MonoGame\TurnBasedRPG\TurnBased_20250822\TurnBasedRPG\bin\Debug\net8.0\runtimes\osx\native\libopenal.1.dylib
C:\Projects\MonoGame\TurnBasedRPG\TurnBased_20250822\TurnBasedRPG\bin\Debug\net8.0\runtimes\win-x64\native\soft_oal.dll
C:\Projects\MonoGame\TurnBasedRPG\TurnBased_20250822\TurnBasedRPG\bin\Debug\net8.0\runtimes\win-x86\native\soft_oal.dll
C:\Projects\MonoGame\TurnBasedRPG\TurnBased_20250822\TurnBasedRPG\bin\Debug\net8.0\runtimes\linux-x64\native\libSDL2-2.0.so.0
C:\Projects\MonoGame\TurnBasedRPG\TurnBased_20250822\TurnBasedRPG\bin\Debug\net8.0\runtimes\osx\native\libSDL2.dylib
C:\Projects\MonoGame\TurnBasedRPG\TurnBased_20250822\TurnBasedRPG\bin\Debug\net8.0\runtimes\win-x64\native\SDL2.dll
C:\Projects\MonoGame\TurnBasedRPG\TurnBased_20250822\TurnBasedRPG\bin\Debug\net8.0\Monogame.ECS.dll
C:\Projects\MonoGame\TurnBasedRPG\TurnBased_20250822\TurnBasedRPG\bin\Debug\net8.0\Monogame.Scene.dll
C:\Projects\MonoGame\TurnBasedRPG\TurnBased_20250822\TurnBasedRPG\bin\Debug\net8.0\Monogame.Trigger.dll
C:\Projects\MonoGame\TurnBasedRPG\TurnBased_20250822\TurnBasedRPG\bin\Debug\net8.0\Monogame.ECS.pdb
C:\Projects\MonoGame\TurnBasedRPG\TurnBased_20250822\TurnBasedRPG\bin\Debug\net8.0\Monogame.Scene.pdb
C:\Projects\MonoGame\TurnBasedRPG\TurnBased_20250822\TurnBasedRPG\bin\Debug\net8.0\Monogame.Trigger.pdb
C:\Projects\MonoGame\TurnBasedRPG\TurnBased_20250822\TurnBasedRPG\obj\Debug\net8.0\TurnBase.9A205620.Up2Date
C:\Projects\MonoGame\TurnBasedRPG\TurnBased_20250822\TurnBasedRPG\obj\Debug\net8.0\TurnBasedRPG.dll
C:\Projects\MonoGame\TurnBasedRPG\TurnBased_20250822\TurnBasedRPG\obj\Debug\net8.0\refint\TurnBasedRPG.dll
C:\Projects\MonoGame\TurnBasedRPG\TurnBased_20250822\TurnBasedRPG\obj\Debug\net8.0\TurnBasedRPG.pdb
C:\Projects\MonoGame\TurnBasedRPG\TurnBased_20250822\TurnBasedRPG\obj\Debug\net8.0\TurnBasedRPG.genruntimeconfig.cache
C:\Projects\MonoGame\TurnBasedRPG\TurnBased_20250822\TurnBasedRPG\obj\Debug\net8.0\ref\TurnBasedRPG.dll
