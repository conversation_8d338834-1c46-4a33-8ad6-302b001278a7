﻿using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;
using Monogame.ECS.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Monogame.Scene.Core
{
    public class SceneManager
    {
        private readonly Dictionary<string, IScene> _scenes;
        private IScene _currentScene;
        private IScene _nextScene;

        public World World { get; private set; }
        public GraphicsDevice GraphicsDevice { get; }
        public SpriteBatch SpriteBatch { get; }

        public SceneManager(GraphicsDevice graphicsDevice, SpriteBatch spriteBatch)
        {
            _scenes = new Dictionary<string, IScene>();
            GraphicsDevice = graphicsDevice;
            SpriteBatch = spriteBatch;
            World = new World();
        }

        public void AddScene(string name, IScene scene)
        {
            _scenes[name] = scene;
        }

        public void SwitchToScene(string name)
        {
            if (_scenes.TryGetValue(name, out var scene))
            {
                _nextScene = scene;
            }
        }

        public void Update(GameTime gameTime)
        {
            // Handle scene transitions
            if (_nextScene != null)
            {
                _currentScene?.Exit();

                // Reset world for new scene (or preserve if needed)
                World = new World(); // Or implement world cleanup/preservation logic
                _nextScene.World = World;
                _nextScene.Initialize();

                _currentScene = _nextScene;
                _nextScene = null;
            }

            _currentScene?.Update(gameTime);
            World.Update(gameTime);
        }

        public void Draw(GameTime gameTime)
        {
            _currentScene?.Draw(gameTime);
        }
    }
}
