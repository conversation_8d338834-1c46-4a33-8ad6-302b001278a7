﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Monogame.ECS.Core.BaseClasses
{
    public interface ISystem
    {
        /// <summary>
        /// Called once when system is added to the world
        /// </summary>
        void Initialize();

        /// <summary>
        /// Called every frame
        /// </summary>
        void Update(Microsoft.Xna.Framework.GameTime gameTime);

        /// <summary>
        /// Returns a list of component types this system requires
        /// </summary>
        IEnumerable<Type> RequiredComponentTypes { get; }
    }
}
