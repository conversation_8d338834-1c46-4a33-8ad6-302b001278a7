using Monogame.ECS.Core.BaseClasses;

namespace Monogame.ECS.Components
{
    /// <summary>
    /// Marker component to identify player entities
    /// </summary>
    public class PlayerComponent : Component
    {
        public int PlayerId { get; set; }
        public string PlayerName { get; set; }

        public PlayerComponent()
        {
            PlayerId = 1;
            PlayerName = "Player";
        }

        public PlayerComponent(int playerId, string playerName = "Player") : this()
        {
            PlayerId = playerId;
            PlayerName = playerName;
        }
    }
}
