using System;
using System.Collections.Generic;
using System.Linq;
using Monogame.ECS.Core.BaseClasses;

namespace Monogame.ECS.Components
{
    public enum AbilityType
    {
        <PERSON>,
        Heal,
        Buff,
        Debuff,
        Utility,
        Movement
    }

    public enum TargetType
    {
        Self,
        SingleEnemy,
        SingleAlly,
        AllEnemies,
        AllAllies,
        Area
    }

    public class Ability
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public AbilityType Type { get; set; }
        public TargetType TargetType { get; set; }
        public int ManaCost { get; set; }
        public int ActionPointCost { get; set; }
        public float Cooldown { get; set; }
        public float Range { get; set; }
        public int Damage { get; set; }
        public int Healing { get; set; }
        public float Duration { get; set; }
        public int RequiredLevel { get; set; }
        public bool IsLearned { get; set; }

        public Ability(string id, string name, AbilityType type, TargetType targetType)
        {
            Id = id;
            Name = name;
            Type = type;
            TargetType = targetType;
            Description = "";
            ManaCost = 0;
            ActionPointCost = 1;
            Cooldown = 0f;
            Range = 1f;
            Damage = 0;
            Healing = 0;
            Duration = 0f;
            RequiredLevel = 1;
            IsLearned = false;
        }
    }

    public class AbilityCooldown
    {
        public string AbilityId { get; set; }
        public float RemainingTime { get; set; }

        public AbilityCooldown(string abilityId, float cooldownTime)
        {
            AbilityId = abilityId;
            RemainingTime = cooldownTime;
        }
    }

    public class AbilityComponent : Component
    {
        public List<Ability> KnownAbilities { get; set; }
        public List<AbilityCooldown> Cooldowns { get; set; }
        public int CurrentMana { get; set; }
        public int MaxMana { get; set; }
        public float ManaRegenRate { get; set; } // Mana per second

        public float ManaPercentage => MaxMana > 0 ? (float)CurrentMana / MaxMana : 0f;

        public AbilityComponent()
        {
            KnownAbilities = new List<Ability>();
            Cooldowns = new List<AbilityCooldown>();
            CurrentMana = 100;
            MaxMana = 100;
            ManaRegenRate = 1f; // 1 mana per second
        }

        public AbilityComponent(int maxMana, float manaRegenRate = 1f) : this()
        {
            MaxMana = maxMana;
            CurrentMana = maxMana;
            ManaRegenRate = manaRegenRate;
        }

        public void LearnAbility(Ability ability)
        {
            if (!KnownAbilities.Any(a => a.Id == ability.Id))
            {
                ability.IsLearned = true;
                KnownAbilities.Add(ability);
            }
        }

        public void ForgetAbility(string abilityId)
        {
            KnownAbilities.RemoveAll(a => a.Id == abilityId);
            Cooldowns.RemoveAll(c => c.AbilityId == abilityId);
        }

        public bool CanUseAbility(string abilityId)
        {
            var ability = GetAbility(abilityId);
            if (ability == null || !ability.IsLearned)
                return false;

            // Check mana cost
            if (CurrentMana < ability.ManaCost)
                return false;

            // Check cooldown
            if (IsOnCooldown(abilityId))
                return false;

            return true;
        }

        public bool UseAbility(string abilityId)
        {
            if (!CanUseAbility(abilityId))
                return false;

            var ability = GetAbility(abilityId);
            
            // Consume mana
            CurrentMana -= ability.ManaCost;
            
            // Start cooldown
            if (ability.Cooldown > 0)
            {
                Cooldowns.Add(new AbilityCooldown(abilityId, ability.Cooldown));
            }

            return true;
        }

        public Ability GetAbility(string abilityId)
        {
            return KnownAbilities.FirstOrDefault(a => a.Id == abilityId);
        }

        public bool IsOnCooldown(string abilityId)
        {
            return Cooldowns.Any(c => c.AbilityId == abilityId);
        }

        public float GetCooldownRemaining(string abilityId)
        {
            var cooldown = Cooldowns.FirstOrDefault(c => c.AbilityId == abilityId);
            return cooldown?.RemainingTime ?? 0f;
        }

        public void UpdateCooldowns(float deltaTime)
        {
            for (int i = Cooldowns.Count - 1; i >= 0; i--)
            {
                Cooldowns[i].RemainingTime -= deltaTime;
                if (Cooldowns[i].RemainingTime <= 0)
                {
                    Cooldowns.RemoveAt(i);
                }
            }
        }

        public void RegenerateMana(float deltaTime)
        {
            if (CurrentMana < MaxMana)
            {
                CurrentMana = Math.Min(MaxMana, CurrentMana + (int)(ManaRegenRate * deltaTime));
            }
        }

        public void RestoreMana(int amount)
        {
            CurrentMana = Math.Min(MaxMana, CurrentMana + amount);
        }

        public void SetMaxMana(int newMaxMana, bool restoreToFull = false)
        {
            MaxMana = newMaxMana;
            if (restoreToFull)
            {
                CurrentMana = MaxMana;
            }
            else
            {
                CurrentMana = Math.Min(CurrentMana, MaxMana);
            }
        }

        public List<Ability> GetUsableAbilities()
        {
            return KnownAbilities.Where(a => CanUseAbility(a.Id)).ToList();
        }
    }
}
