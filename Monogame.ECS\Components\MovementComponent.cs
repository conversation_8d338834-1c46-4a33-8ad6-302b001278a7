using Microsoft.Xna.Framework;
using Monogame.ECS.Core.BaseClasses;

namespace Monogame.ECS.Components
{
    public class MovementComponent : Component
    {
        public Vector2 Velocity { get; set; }
        public float Speed { get; set; }
        public Vector2 Direction { get; set; }
        public bool CanMove { get; set; }
        public bool IsMoving => Velocity.LengthSquared() > 0.01f;

        public MovementComponent()
        {
            Velocity = Vector2.Zero;
            Speed = 100f;
            Direction = Vector2.Zero;
            CanMove = true;
        }

        public MovementComponent(float speed) : this()
        {
            Speed = speed;
        }

        public void SetDirection(Vector2 direction)
        {
            Direction = direction;
            if (Direction.LengthSquared() > 0)
            {
                Direction.Normalize();
            }
        }

        public void UpdateVelocity()
        {
            if (CanMove)
            {
                Velocity = Direction * Speed;
            }
            else
            {
                Velocity = Vector2.Zero;
            }
        }

        public void Stop()
        {
            Velocity = Vector2.Zero;
            Direction = Vector2.Zero;
        }
    }
}
