﻿using Microsoft.Xna.Framework;
using System;
using System.Collections.Generic;

namespace Monogame.ECS.Core.BaseClasses
{
    public abstract class GameSystem : ISystem
    {
        protected EntityManager EntityManager;

        protected GameSystem(EntityManager entityManager)
        {
            EntityManager = entityManager;
        }

        public virtual void Initialize() { }

        public abstract void Update(GameTime gameTime);

        public abstract IEnumerable<Type> RequiredComponentTypes { get; }
    }
}
