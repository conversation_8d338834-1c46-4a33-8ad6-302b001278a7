using System;
using Monogame.ECS.Core.BaseClasses;

namespace Monogame.ECS.Components
{
    public enum TurnState
    {
        Waiting,
        Active,
        ActionSelected,
        Executing,
        Finished
    }

    public class TurnComponent : Component
    {
        public int Initiative { get; set; }
        public int ActionPoints { get; set; }
        public int MaxActionPoints { get; set; }
        public TurnState State { get; set; }
        public bool HasActed { get; set; }
        public bool CanAct => ActionPoints > 0 && !HasActed && State == TurnState.Active;

        public TurnComponent()
        {
            Initiative = 0;
            ActionPoints = 1;
            MaxActionPoints = 1;
            State = TurnState.Waiting;
            HasActed = false;
        }

        public TurnComponent(int initiative, int maxActionPoints = 1) : this()
        {
            Initiative = initiative;
            MaxActionPoints = maxActionPoints;
            ActionPoints = maxActionPoints;
        }

        public void StartTurn()
        {
            State = TurnState.Active;
            HasActed = false;
            ActionPoints = MaxActionPoints;
        }

        public void EndTurn()
        {
            State = TurnState.Finished;
            HasActed = true;
            ActionPoints = 0;
        }

        public bool UseActionPoint()
        {
            if (ActionPoints > 0)
            {
                ActionPoints--;
                return true;
            }
            return false;
        }

        public void ResetForNewRound()
        {
            State = TurnState.Waiting;
            HasActed = false;
            ActionPoints = MaxActionPoints;
        }

        public void SetState(TurnState newState)
        {
            State = newState;
        }
    }
}
