using System;
using System.Collections.Generic;
using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Input;
using Monogame.ECS.Core;
using Monogame.ECS.Core.BaseClasses;
using Monogame.ECS.Components;

namespace Monogame.ECS.System
{
    public class InputSystem : ISystem
    {
        private readonly EntityManager _entityManager;
        private KeyboardState _previousKeyboardState;
        private KeyboardState _currentKeyboardState;
        private MouseState _previousMouseState;
        private MouseState _currentMouseState;

        public IEnumerable<Type> RequiredComponentTypes => new[]
        {
            typeof(PlayerComponent)
        };

        // Events for input handling
        public event Action<Keys> OnKeyPressed;
        public event Action<Keys> OnKeyReleased;
        public event Action<Vector2> OnMouseClick;
        public event Action<Vector2> OnMouseMove;

        public InputSystem(EntityManager entityManager)
        {
            _entityManager = entityManager;
            _currentKeyboardState = Keyboard.GetState();
            _currentMouseState = Mouse.GetState();
        }

        public void Initialize()
        {
            // No initialization needed
        }

        public void Update(GameTime gameTime)
        {
            // Update input states
            _previousKeyboardState = _currentKeyboardState;
            _previousMouseState = _currentMouseState;
            _currentKeyboardState = Keyboard.GetState();
            _currentMouseState = Mouse.GetState();

            // Handle keyboard input
            HandleKeyboardInput();

            // Handle mouse input
            HandleMouseInput();

            // Handle player movement
            HandlePlayerMovement();
        }

        private void HandleKeyboardInput()
        {
            // Check for key presses and releases
            var pressedKeys = _currentKeyboardState.GetPressedKeys();
            var previousPressedKeys = _previousKeyboardState.GetPressedKeys();

            // Find newly pressed keys
            foreach (var key in pressedKeys)
            {
                if (!_previousKeyboardState.IsKeyDown(key))
                {
                    OnKeyPressed?.Invoke(key);
                    HandleKeyPress(key);
                }
            }

            // Find newly released keys
            foreach (var key in previousPressedKeys)
            {
                if (!_currentKeyboardState.IsKeyDown(key))
                {
                    OnKeyReleased?.Invoke(key);
                    HandleKeyRelease(key);
                }
            }
        }

        private void HandleMouseInput()
        {
            var mousePosition = new Vector2(_currentMouseState.X, _currentMouseState.Y);

            // Handle mouse movement
            if (_currentMouseState.X != _previousMouseState.X || 
                _currentMouseState.Y != _previousMouseState.Y)
            {
                OnMouseMove?.Invoke(mousePosition);
            }

            // Handle mouse clicks
            if (_currentMouseState.LeftButton == ButtonState.Pressed && 
                _previousMouseState.LeftButton == ButtonState.Released)
            {
                OnMouseClick?.Invoke(mousePosition);
                HandleMouseClick(mousePosition);
            }
        }

        private void HandlePlayerMovement()
        {
            var players = _entityManager.GetEntitiesWithComponent<PlayerComponent>();

            foreach (var player in players)
            {
                var movement = player.GetComponent<MovementComponent>();
                if (movement == null || !movement.CanMove)
                    continue;

                Vector2 direction = Vector2.Zero;

                // WASD or Arrow key movement
                if (_currentKeyboardState.IsKeyDown(Keys.W) || _currentKeyboardState.IsKeyDown(Keys.Up))
                    direction.Y -= 1;
                if (_currentKeyboardState.IsKeyDown(Keys.S) || _currentKeyboardState.IsKeyDown(Keys.Down))
                    direction.Y += 1;
                if (_currentKeyboardState.IsKeyDown(Keys.A) || _currentKeyboardState.IsKeyDown(Keys.Left))
                    direction.X -= 1;
                if (_currentKeyboardState.IsKeyDown(Keys.D) || _currentKeyboardState.IsKeyDown(Keys.Right))
                    direction.X += 1;

                // Normalize diagonal movement
                if (direction.LengthSquared() > 0)
                {
                    direction.Normalize();
                }

                movement.SetDirection(direction);
            }
        }

        private void HandleKeyPress(Keys key)
        {
            var players = _entityManager.GetEntitiesWithComponent<PlayerComponent>();

            foreach (var player in players)
            {
                switch (key)
                {
                    case Keys.Space:
                        HandlePlayerAction(player);
                        break;
                    case Keys.E:
                        HandlePlayerInteract(player);
                        break;
                    case Keys.I:
                        HandleInventoryToggle(player);
                        break;
                    case Keys.Tab:
                        HandleTargetCycle(player);
                        break;
                    case Keys.Enter:
                        HandleConfirmAction(player);
                        break;
                    case Keys.Escape:
                        HandleCancelAction(player);
                        break;
                    case Keys.D1:
                    case Keys.D2:
                    case Keys.D3:
                    case Keys.D4:
                    case Keys.D5:
                        HandleAbilityHotkey(player, key);
                        break;
                }
            }
        }

        private void HandleKeyRelease(Keys key)
        {
            // Handle key release events if needed
        }

        private void HandleMouseClick(Vector2 mousePosition)
        {
            var players = _entityManager.GetEntitiesWithComponent<PlayerComponent>();

            foreach (var player in players)
            {
                // Handle mouse click for player actions
                // This could be used for targeting, movement, or UI interaction
                HandlePlayerMouseAction(player, mousePosition);
            }
        }

        private void HandlePlayerAction(Entity player)
        {
            // Handle primary action (attack, use ability, etc.)
            var abilities = player.GetComponent<AbilityComponent>();
            if (abilities != null)
            {
                // Use basic attack or first available ability
                var basicAttack = abilities.GetUsableAbilities().FirstOrDefault();
                if (basicAttack != null)
                {
                    abilities.UseAbility(basicAttack.Id);
                }
            }
        }

        private void HandlePlayerInteract(Entity player)
        {
            // Handle interaction with nearby objects
            var transform = player.GetComponent<TransformComponent>();
            var collision = player.GetComponent<CollisionComponent>();

            if (transform != null)
            {
                // Find nearby interactable entities
                var nearbyEntities = _entityManager.GetEntitiesWithComponent<CollisionComponent>()
                    .Where(e => e.Id != player.Id);

                foreach (var entity in nearbyEntities)
                {
                    var entityTransform = entity.GetComponent<TransformComponent>();
                    var entityCollision = entity.GetComponent<CollisionComponent>();

                    if (entityTransform != null && entityCollision != null)
                    {
                        var distance = Vector2.Distance(transform.Position, entityTransform.Position);
                        if (distance <= 50f) // Interaction range
                        {
                            HandleEntityInteraction(player, entity);
                            break; // Only interact with first nearby entity
                        }
                    }
                }
            }
        }

        private void HandleInventoryToggle(Entity player)
        {
            // Toggle inventory UI (this would typically be handled by a UI system)
            var inventory = player.GetComponent<InventoryComponent>();
            if (inventory != null)
            {
                // Trigger inventory UI toggle event
                // This is a placeholder - actual UI handling would be in a UI system
            }
        }

        private void HandleTargetCycle(Entity player)
        {
            // Cycle through available targets
            // This would typically be used in combat to select different enemies
        }

        private void HandleConfirmAction(Entity player)
        {
            // Confirm current action or selection
        }

        private void HandleCancelAction(Entity player)
        {
            // Cancel current action or close menus
        }

        private void HandleAbilityHotkey(Entity player, Keys key)
        {
            var abilities = player.GetComponent<AbilityComponent>();
            if (abilities == null)
                return;

            // Map number keys to ability slots
            int abilityIndex = key - Keys.D1;
            var usableAbilities = abilities.GetUsableAbilities();

            if (abilityIndex >= 0 && abilityIndex < usableAbilities.Count)
            {
                var ability = usableAbilities[abilityIndex];
                abilities.UseAbility(ability.Id);
            }
        }

        private void HandlePlayerMouseAction(Entity player, Vector2 mousePosition)
        {
            // Handle mouse-based actions like targeting or movement
            // This could be used for click-to-move or click-to-target functionality
        }

        private void HandleEntityInteraction(Entity player, Entity target)
        {
            // Handle interaction between player and target entity
            var targetCollision = target.GetComponent<CollisionComponent>();

            switch (targetCollision.Type)
            {
                case CollisionType.Item:
                    // Pick up item
                    var inventory = player.GetComponent<InventoryComponent>();
                    if (inventory != null)
                    {
                        var item = new InventoryItem("item_" + target.Id, target.Name ?? "Item", 1);
                        if (inventory.AddItem(item))
                        {
                            _entityManager.RemoveEntity(target);
                        }
                    }
                    break;

                case CollisionType.Enemy:
                    // Initiate combat or dialogue
                    break;

                default:
                    // Generic interaction
                    break;
            }
        }

        // Helper methods for external systems
        public bool IsKeyDown(Keys key)
        {
            return _currentKeyboardState.IsKeyDown(key);
        }

        public bool IsKeyPressed(Keys key)
        {
            return _currentKeyboardState.IsKeyDown(key) && !_previousKeyboardState.IsKeyDown(key);
        }

        public bool IsKeyReleased(Keys key)
        {
            return !_currentKeyboardState.IsKeyDown(key) && _previousKeyboardState.IsKeyDown(key);
        }

        public Vector2 GetMousePosition()
        {
            return new Vector2(_currentMouseState.X, _currentMouseState.Y);
        }

        public bool IsMouseButtonPressed(MouseButton button)
        {
            switch (button)
            {
                case MouseButton.Left:
                    return _currentMouseState.LeftButton == ButtonState.Pressed && 
                           _previousMouseState.LeftButton == ButtonState.Released;
                case MouseButton.Right:
                    return _currentMouseState.RightButton == ButtonState.Pressed && 
                           _previousMouseState.RightButton == ButtonState.Released;
                case MouseButton.Middle:
                    return _currentMouseState.MiddleButton == ButtonState.Pressed && 
                           _previousMouseState.MiddleButton == ButtonState.Released;
                default:
                    return false;
            }
        }
    }

    public enum MouseButton
    {
        Left,
        Right,
        Middle
    }
}
