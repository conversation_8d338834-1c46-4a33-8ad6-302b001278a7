using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Xna.Framework;
using Monogame.ECS.Core;
using Monogame.ECS.Core.BaseClasses;
using Monogame.ECS.Components;

namespace Monogame.ECS.System
{
    public class TurnBasedSystem : ISystem
    {
        private readonly EntityManager _entityManager;
        private List<Entity> _turnOrder;
        private int _currentTurnIndex;
        private bool _isTurnActive;
        private float _turnTimer;
        private float _maxTurnTime;

        public IEnumerable<Type> RequiredComponentTypes => new[]
        {
            typeof(TurnComponent)
        };

        // Events for turn management
        public event Action<Entity> OnTurnStart;
        public event Action<Entity> OnTurnEnd;
        public event Action OnRoundStart;
        public event Action OnRoundEnd;
        public event Action<Entity> OnEntityAction;

        public Entity CurrentTurnEntity => _turnOrder != null && _currentTurnIndex < _turnOrder.Count 
            ? _turnOrder[_currentTurnIndex] 
            : null;

        public bool IsTurnActive => _isTurnActive;
        public float RemainingTurnTime => Math.Max(0, _maxTurnTime - _turnTimer);

        public TurnBasedSystem(EntityManager entityManager)
        {
            _entityManager = entityManager;
            _turnOrder = new List<Entity>();
            _currentTurnIndex = 0;
            _isTurnActive = false;
            _turnTimer = 0f;
            _maxTurnTime = 30f; // 30 seconds per turn by default
        }

        public void Initialize()
        {
            InitializeTurnOrder();
        }

        public void Update(GameTime gameTime)
        {
            if (!_isTurnActive)
                return;

            float deltaTime = (float)gameTime.ElapsedGameTime.TotalSeconds;
            _turnTimer += deltaTime;

            // Check for turn timeout
            if (_turnTimer >= _maxTurnTime)
            {
                EndCurrentTurn();
            }

            // Update current turn entity
            var currentEntity = CurrentTurnEntity;
            if (currentEntity != null)
            {
                var turnComponent = currentEntity.GetComponent<TurnComponent>();
                
                // Auto-end turn if entity has no action points left
                if (turnComponent.ActionPoints <= 0 || turnComponent.HasActed)
                {
                    EndCurrentTurn();
                }
            }
        }

        public void StartCombat()
        {
            InitializeTurnOrder();
            StartNewRound();
        }

        public void EndCombat()
        {
            _isTurnActive = false;
            
            // Reset all turn components
            var turnEntities = _entityManager.GetEntitiesWithComponent<TurnComponent>();
            foreach (var entity in turnEntities)
            {
                var turn = entity.GetComponent<TurnComponent>();
                turn.ResetForNewRound();
            }
        }

        private void InitializeTurnOrder()
        {
            var turnEntities = _entityManager.GetEntitiesWithComponent<TurnComponent>()
                .Where(e => e.GetComponent<HealthComponent>()?.IsAlive ?? true)
                .ToList();

            // Sort by initiative (highest first)
            _turnOrder = turnEntities
                .OrderByDescending(e => e.GetComponent<TurnComponent>().Initiative)
                .ThenByDescending(e => e.GetComponent<StatsComponent>()?.Agility ?? 0)
                .ToList();

            _currentTurnIndex = 0;
        }

        private void StartNewRound()
        {
            OnRoundStart?.Invoke();

            // Reset all entities for new round
            foreach (var entity in _turnOrder)
            {
                var turn = entity.GetComponent<TurnComponent>();
                turn.ResetForNewRound();
            }

            _currentTurnIndex = 0;
            StartNextTurn();
        }

        private void StartNextTurn()
        {
            if (_turnOrder.Count == 0)
            {
                EndCombat();
                return;
            }

            // Find next valid entity
            while (_currentTurnIndex < _turnOrder.Count)
            {
                var entity = _turnOrder[_currentTurnIndex];
                var health = entity.GetComponent<HealthComponent>();
                
                // Skip dead entities
                if (health != null && !health.IsAlive)
                {
                    _currentTurnIndex++;
                    continue;
                }

                var turn = entity.GetComponent<TurnComponent>();
                turn.StartTurn();
                
                _isTurnActive = true;
                _turnTimer = 0f;
                
                OnTurnStart?.Invoke(entity);
                
                // Auto-handle AI turns
                if (entity.HasComponent<AIComponent>())
                {
                    HandleAITurn(entity);
                }
                
                return;
            }

            // All entities have had their turn, start new round
            OnRoundEnd?.Invoke();
            StartNewRound();
        }

        public void EndCurrentTurn()
        {
            var currentEntity = CurrentTurnEntity;
            if (currentEntity != null)
            {
                var turn = currentEntity.GetComponent<TurnComponent>();
                turn.EndTurn();
                
                OnTurnEnd?.Invoke(currentEntity);
            }

            _currentTurnIndex++;
            StartNextTurn();
        }

        public bool TryUseActionPoint(Entity entity)
        {
            if (entity.Id != CurrentTurnEntity?.Id)
                return false;

            var turn = entity.GetComponent<TurnComponent>();
            if (turn.UseActionPoint())
            {
                OnEntityAction?.Invoke(entity);
                return true;
            }

            return false;
        }

        public bool CanEntityAct(Entity entity)
        {
            if (entity.Id != CurrentTurnEntity?.Id)
                return false;

            var turn = entity.GetComponent<TurnComponent>();
            return turn.CanAct;
        }

        private void HandleAITurn(Entity aiEntity)
        {
            var ai = aiEntity.GetComponent<AIComponent>();
            var turn = aiEntity.GetComponent<TurnComponent>();

            if (!ai.IsEnabled || !turn.CanAct)
            {
                EndCurrentTurn();
                return;
            }

            // Simple AI logic - this could be much more sophisticated
            switch (ai.BehaviorType)
            {
                case AIBehaviorType.Aggressive:
                    HandleAggressiveAI(aiEntity);
                    break;
                case AIBehaviorType.Defensive:
                    HandleDefensiveAI(aiEntity);
                    break;
                case AIBehaviorType.Passive:
                    // Passive AI just ends turn
                    EndCurrentTurn();
                    break;
                default:
                    EndCurrentTurn();
                    break;
            }
        }

        private void HandleAggressiveAI(Entity aiEntity)
        {
            // Find nearest player to attack
            var players = _entityManager.GetEntitiesWithComponent<PlayerComponent>();
            var aiTransform = aiEntity.GetComponent<TransformComponent>();
            
            Entity nearestPlayer = null;
            float nearestDistance = float.MaxValue;

            foreach (var player in players)
            {
                var playerTransform = player.GetComponent<TransformComponent>();
                var distance = Vector2.Distance(aiTransform.Position, playerTransform.Position);
                
                if (distance < nearestDistance)
                {
                    nearestDistance = distance;
                    nearestPlayer = player;
                }
            }

            if (nearestPlayer != null)
            {
                // Try to attack if in range, otherwise move closer
                var ai = aiEntity.GetComponent<AIComponent>();
                if (nearestDistance <= ai.AttackRange)
                {
                    // Perform attack
                    PerformAIAttack(aiEntity, nearestPlayer);
                }
                else
                {
                    // Move towards player
                    MoveAITowardsTarget(aiEntity, nearestPlayer);
                }
            }

            EndCurrentTurn();
        }

        private void HandleDefensiveAI(Entity aiEntity)
        {
            // Defensive AI might heal, buff, or move to safety
            var health = aiEntity.GetComponent<HealthComponent>();
            
            if (health != null && health.HealthPercentage < 0.5f)
            {
                // Try to heal if health is low
                TryAIHeal(aiEntity);
            }

            EndCurrentTurn();
        }

        private void PerformAIAttack(Entity attacker, Entity target)
        {
            var attackerStats = attacker.GetComponent<StatsComponent>();
            var targetHealth = target.GetComponent<HealthComponent>();

            if (attackerStats != null && targetHealth != null)
            {
                int damage = attackerStats.AttackPower;
                targetHealth.TakeDamage(damage);
                
                TryUseActionPoint(attacker);
            }
        }

        private void MoveAITowardsTarget(Entity aiEntity, Entity target)
        {
            var aiTransform = aiEntity.GetComponent<TransformComponent>();
            var targetTransform = target.GetComponent<TransformComponent>();
            var movement = aiEntity.GetComponent<MovementComponent>();

            if (movement != null)
            {
                var direction = targetTransform.Position - aiTransform.Position;
                direction.Normalize();
                movement.SetDirection(direction);
                
                TryUseActionPoint(aiEntity);
            }
        }

        private void TryAIHeal(Entity aiEntity)
        {
            var abilities = aiEntity.GetComponent<AbilityComponent>();
            var health = aiEntity.GetComponent<HealthComponent>();

            if (abilities != null && health != null)
            {
                // Look for healing abilities
                var healingAbility = abilities.KnownAbilities
                    .FirstOrDefault(a => a.Type == AbilityType.Heal && abilities.CanUseAbility(a.Id));

                if (healingAbility != null)
                {
                    abilities.UseAbility(healingAbility.Id);
                    health.Heal(healingAbility.Healing);
                    TryUseActionPoint(aiEntity);
                }
            }
        }

        public void SetMaxTurnTime(float seconds)
        {
            _maxTurnTime = seconds;
        }

        public List<Entity> GetTurnOrder()
        {
            return new List<Entity>(_turnOrder);
        }
    }
}
