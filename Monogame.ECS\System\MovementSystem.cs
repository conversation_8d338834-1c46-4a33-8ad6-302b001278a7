using System;
using System.Collections.Generic;
using Microsoft.Xna.Framework;
using Monogame.ECS.Core;
using Monogame.ECS.Core.BaseClasses;
using Monogame.ECS.Components;

namespace Monogame.ECS.System
{
    public class MovementSystem : ISystem
    {
        private readonly EntityManager _entityManager;

        public IEnumerable<Type> RequiredComponentTypes => new[]
        {
            typeof(TransformComponent),
            typeof(MovementComponent)
        };

        public MovementSystem(EntityManager entityManager)
        {
            _entityManager = entityManager;
        }

        public void Initialize()
        {
            // No initialization needed
        }

        public void Update(GameTime gameTime)
        {
            var movableEntities = _entityManager.GetEntitiesWithComponents(
                typeof(TransformComponent),
                typeof(MovementComponent));

            float deltaTime = (float)gameTime.ElapsedGameTime.TotalSeconds;

            foreach (var entity in movableEntities)
            {
                var transform = entity.GetComponent<TransformComponent>();
                var movement = entity.GetComponent<MovementComponent>();

                if (!movement.CanMove)
                    continue;

                // Update velocity based on direction and speed
                movement.UpdateVelocity();

                // Apply velocity to position
                if (movement.Velocity.LengthSquared() > 0)
                {
                    var newPosition = transform.Position + movement.Velocity * deltaTime;
                    
                    // Check for collision if collision component exists
                    var collision = entity.GetComponent<CollisionComponent>();
                    if (collision != null)
                    {
                        if (CanMoveTo(entity, newPosition, collision))
                        {
                            transform.Position = newPosition;
                        }
                        else
                        {
                            // Stop movement if collision detected
                            movement.Stop();
                        }
                    }
                    else
                    {
                        // No collision checking, move freely
                        transform.Position = newPosition;
                    }
                }

                // Update rotation to face movement direction if moving
                if (movement.IsMoving && movement.Direction.LengthSquared() > 0)
                {
                    transform.Rotation = (float)Math.Atan2(movement.Direction.Y, movement.Direction.X);
                }
            }
        }

        private bool CanMoveTo(Entity movingEntity, Vector2 newPosition, CollisionComponent movingCollision)
        {
            if (!movingCollision.IsEnabled)
                return true;

            // Check collision with other entities
            var otherEntities = _entityManager.GetEntitiesWithComponent<CollisionComponent>()
                .Where(e => e.Id != movingEntity.Id);

            foreach (var otherEntity in otherEntities)
            {
                var otherCollision = otherEntity.GetComponent<CollisionComponent>();
                var otherTransform = otherEntity.GetComponent<TransformComponent>();

                if (otherTransform == null || !otherCollision.IsEnabled)
                    continue;

                if (movingCollision.Intersects(otherCollision, newPosition, otherTransform.Position))
                {
                    // Handle different collision types
                    if (otherCollision.Type == CollisionType.Solid || 
                        otherCollision.Type == CollisionType.Wall)
                    {
                        return false; // Block movement
                    }
                    else if (otherCollision.Type == CollisionType.Trigger)
                    {
                        // Trigger collision event but allow movement
                        HandleTriggerCollision(movingEntity, otherEntity);
                    }
                }
            }

            return true;
        }

        private void HandleTriggerCollision(Entity triggerEntity, Entity otherEntity)
        {
            // This could be expanded to handle specific trigger events
            // For now, just a placeholder for trigger logic
            
            // Example: If other entity is an item, pick it up
            if (otherEntity.GetComponent<CollisionComponent>().Type == CollisionType.Item)
            {
                // Handle item pickup logic here
                // This would typically involve adding to inventory and removing the item entity
            }
        }

        public void MoveEntity(Entity entity, Vector2 direction)
        {
            var movement = entity.GetComponent<MovementComponent>();
            if (movement != null && movement.CanMove)
            {
                movement.SetDirection(direction);
            }
        }

        public void StopEntity(Entity entity)
        {
            var movement = entity.GetComponent<MovementComponent>();
            movement?.Stop();
        }

        public void SetEntitySpeed(Entity entity, float speed)
        {
            var movement = entity.GetComponent<MovementComponent>();
            if (movement != null)
            {
                movement.Speed = speed;
            }
        }
    }
}
