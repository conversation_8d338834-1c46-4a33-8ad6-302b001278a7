using System;
using System.Collections.Generic;
using Microsoft.Xna.Framework;
using Monogame.Trigger.Core;

namespace Monogame.Trigger.System
{
    /// <summary>
    /// ECS System that manages event processing within the game loop
    /// This system doesn't require any specific components but processes events for all systems
    /// </summary>
    public class EventSystem
    {
        private readonly EventManager _eventManager;
        private readonly int _maxEventsPerFrame;
        private readonly TimeSpan _eventCleanupInterval;
        private DateTime _lastCleanupTime;

        public EventManager EventManager => _eventManager;
        public int QueuedEventCount => _eventManager.QueuedEventCount;
        public int SubscriptionCount => _eventManager.SubscriptionCount;

        public EventSystem(int maxQueueSize = 1000, int maxEventsPerFrame = 50)
        {
            _eventManager = new EventManager(maxQueueSize);
            _maxEventsPerFrame = maxEventsPerFrame;
            _eventCleanupInterval = TimeSpan.FromMinutes(5); // Cleanup old events every 5 minutes
            _lastCleanupTime = DateTime.UtcNow;
        }

        /// <summary>
        /// Initialize the event system
        /// </summary>
        public void Initialize()
        {
            // No specific initialization needed
        }

        /// <summary>
        /// Update the event system - process queued events
        /// This should be called during the game's update loop
        /// </summary>
        /// <param name="gameTime">Current game time</param>
        public void Update(GameTime gameTime)
        {
            // Process events with a limit to prevent frame drops
            int eventsProcessed = _eventManager.ProcessEvents(_maxEventsPerFrame);

            // Periodic cleanup of old events
            if (DateTime.UtcNow - _lastCleanupTime > _eventCleanupInterval)
            {
                CleanupOldEvents();
                _lastCleanupTime = DateTime.UtcNow;
            }
        }

        /// <summary>
        /// Subscribe to events of a specific type
        /// </summary>
        /// <typeparam name="T">The event type to subscribe to</typeparam>
        /// <param name="handler">The callback to execute when the event is triggered</param>
        /// <returns>Subscription handle for unsubscribing</returns>
        public IEventSubscription Subscribe<T>(Action<T> handler) where T : class, IEvent
        {
            return _eventManager.Subscribe(handler);
        }

        /// <summary>
        /// Unsubscribe from events
        /// </summary>
        /// <param name="subscription">The subscription to remove</param>
        public void Unsubscribe(IEventSubscription subscription)
        {
            _eventManager.Unsubscribe(subscription);
        }

        /// <summary>
        /// Queue an event for processing
        /// </summary>
        /// <param name="eventToQueue">The event to queue</param>
        /// <returns>True if successfully queued</returns>
        public bool QueueEvent(IEvent eventToQueue)
        {
            return _eventManager.QueueEvent(eventToQueue);
        }

        /// <summary>
        /// Fire an event immediately (bypasses the queue)
        /// </summary>
        /// <param name="eventToFire">The event to fire immediately</param>
        public void FireEventImmediate(IEvent eventToFire)
        {
            if (eventToFire != null)
            {
                // Create an immediate version of the event
                var immediateEvent = new ImmediateEventWrapper(eventToFire);
                _eventManager.QueueEvent(immediateEvent);
            }
        }

        /// <summary>
        /// Get the number of subscribers for a specific event type
        /// </summary>
        /// <typeparam name="T">The event type</typeparam>
        /// <returns>Number of subscribers</returns>
        public int GetSubscriberCount<T>() where T : class, IEvent
        {
            return _eventManager.GetSubscriberCount<T>();
        }

        /// <summary>
        /// Clear all subscriptions and queued events
        /// </summary>
        public void Clear()
        {
            _eventManager.Clear();
        }

        /// <summary>
        /// Clean up old events from the queue
        /// </summary>
        /// <param name="maxAge">Maximum age of events to keep (default: 1 minute)</param>
        /// <returns>Number of events removed</returns>
        public int CleanupOldEvents(TimeSpan? maxAge = null)
        {
            var cleanupAge = maxAge ?? TimeSpan.FromMinutes(1);
            return _eventManager.CleanupOldEvents(cleanupAge);
        }

        /// <summary>
        /// Get debug information about the event system
        /// </summary>
        /// <returns>Debug information string</returns>
        public string GetDebugInfo()
        {
            return $"EventSystem - Queued: {QueuedEventCount}, Subscriptions: {SubscriptionCount}";
        }
    }

    /// <summary>
    /// Wrapper to make any event immediate
    /// </summary>
    internal class ImmediateEventWrapper : IEvent
    {
        private readonly IEvent _wrappedEvent;

        public Guid EventId => _wrappedEvent.EventId;
        public DateTime Timestamp => _wrappedEvent.Timestamp;
        public int Priority => _wrappedEvent.Priority;
        public bool IsImmediate => true; // Force immediate processing
        public string Source => _wrappedEvent.Source;

        public ImmediateEventWrapper(IEvent eventToWrap)
        {
            _wrappedEvent = eventToWrap ?? throw new ArgumentNullException(nameof(eventToWrap));
        }

        public IEvent GetWrappedEvent()
        {
            return _wrappedEvent;
        }
    }

    /// <summary>
    /// Helper class for systems that need to manage multiple event subscriptions
    /// </summary>
    public class SystemEventSubscriptions : IDisposable
    {
        private readonly EventSubscriptionManager _subscriptionManager;
        private readonly EventSystem _eventSystem;
        private bool _disposed;

        public int SubscriptionCount => _subscriptionManager.SubscriptionCount;

        public SystemEventSubscriptions(EventSystem eventSystem)
        {
            _eventSystem = eventSystem ?? throw new ArgumentNullException(nameof(eventSystem));
            _subscriptionManager = new EventSubscriptionManager();
        }

        /// <summary>
        /// Subscribe to an event type
        /// </summary>
        /// <typeparam name="T">The event type</typeparam>
        /// <param name="handler">The event handler</param>
        /// <returns>The subscription handle</returns>
        public IEventSubscription Subscribe<T>(Action<T> handler) where T : class, IEvent
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(SystemEventSubscriptions));

            return _subscriptionManager.Subscribe(_eventSystem.EventManager, handler);
        }

        /// <summary>
        /// Queue an event
        /// </summary>
        /// <param name="eventToQueue">The event to queue</param>
        /// <returns>True if successfully queued</returns>
        public bool QueueEvent(IEvent eventToQueue)
        {
            if (_disposed)
                return false;

            return _eventSystem.QueueEvent(eventToQueue);
        }

        /// <summary>
        /// Fire an event immediately
        /// </summary>
        /// <param name="eventToFire">The event to fire</param>
        public void FireEventImmediate(IEvent eventToFire)
        {
            if (!_disposed)
            {
                _eventSystem.FireEventImmediate(eventToFire);
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _subscriptionManager.Dispose();
                _disposed = true;
            }
        }
    }
}
