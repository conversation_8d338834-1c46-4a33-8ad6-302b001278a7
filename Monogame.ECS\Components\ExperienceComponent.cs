using System;
using Monogame.ECS.Core.BaseClasses;

namespace Monogame.ECS.Components
{
    public class ExperienceComponent : Component
    {
        public int Level { get; set; }
        public int CurrentExperience { get; set; }
        public int ExperienceToNextLevel { get; set; }
        public int TotalExperience { get; set; }
        public int StatPointsAvailable { get; set; }

        public float ExperiencePercentage => ExperienceToNextLevel > 0 
            ? (float)CurrentExperience / ExperienceToNextLevel 
            : 1f;

        public ExperienceComponent()
        {
            Level = 1;
            CurrentExperience = 0;
            ExperienceToNextLevel = CalculateExperienceForLevel(2);
            TotalExperience = 0;
            StatPointsAvailable = 0;
        }

        public ExperienceComponent(int startingLevel) : this()
        {
            Level = Math.Max(1, startingLevel);
            ExperienceToNextLevel = CalculateExperienceForLevel(Level + 1);
        }

        public bool AddExperience(int amount)
        {
            if (amount <= 0)
                return false;

            CurrentExperience += amount;
            TotalExperience += amount;
            
            bool leveledUp = false;

            // Check for level ups
            while (CurrentExperience >= ExperienceToNextLevel)
            {
                LevelUp();
                leveledUp = true;
            }

            return leveledUp;
        }

        private void LevelUp()
        {
            CurrentExperience -= ExperienceToNextLevel;
            Level++;
            StatPointsAvailable += GetStatPointsPerLevel();
            ExperienceToNextLevel = CalculateExperienceForLevel(Level + 1);
        }

        public static int CalculateExperienceForLevel(int level)
        {
            // Simple exponential formula: 100 * level^1.5
            return (int)(100 * Math.Pow(level, 1.5));
        }

        public int GetStatPointsPerLevel()
        {
            // Give more stat points at higher levels
            if (Level <= 10)
                return 3;
            else if (Level <= 25)
                return 4;
            else
                return 5;
        }

        public void SpendStatPoint()
        {
            if (StatPointsAvailable > 0)
            {
                StatPointsAvailable--;
            }
        }

        public void AddStatPoints(int points)
        {
            StatPointsAvailable += Math.Max(0, points);
        }

        public int GetTotalExperienceForLevel(int level)
        {
            int total = 0;
            for (int i = 2; i <= level; i++)
            {
                total += CalculateExperienceForLevel(i);
            }
            return total;
        }

        public void SetLevel(int newLevel, bool giveStatPoints = true)
        {
            if (newLevel < 1)
                return;

            int oldLevel = Level;
            Level = newLevel;
            TotalExperience = GetTotalExperienceForLevel(Level);
            CurrentExperience = 0;
            ExperienceToNextLevel = CalculateExperienceForLevel(Level + 1);

            if (giveStatPoints && newLevel > oldLevel)
            {
                for (int i = oldLevel + 1; i <= newLevel; i++)
                {
                    StatPointsAvailable += GetStatPointsPerLevel();
                }
            }
        }
    }
}
