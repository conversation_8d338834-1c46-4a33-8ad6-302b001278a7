﻿using Microsoft.Xna.Framework;
using Monogame.ECS.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Monogame.Scene.Core
{
    public abstract class BaseScene : IScene
    {
        public World World { get; set; }

        public virtual void Initialize() { }
        public virtual void Update(GameTime gameTime) { }
        public virtual void Draw(GameTime gameTime) { }
        public virtual void Exit() { }
    }
}
