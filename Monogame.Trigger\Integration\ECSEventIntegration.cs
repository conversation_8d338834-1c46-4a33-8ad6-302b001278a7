using System;
using System.Collections.Generic;
using Microsoft.Xna.Framework;
using Monogame.Trigger.Core;
using Monogame.Trigger.Events;
using Monogame.Trigger.System;

namespace Monogame.Trigger.Integration
{
    /// <summary>
    /// Helper class for integrating the event system with ECS components and systems
    /// </summary>
    public static class ECSEventIntegration
    {
        /// <summary>
        /// Create an entity creation event
        /// </summary>
        public static EntityCreatedEvent CreateEntityCreatedEvent(Guid entityId, string entityName, Type[] componentTypes)
        {
            return new EntityCreatedEvent(entityId, entityName, componentTypes);
        }

        /// <summary>
        /// Create an entity destruction event
        /// </summary>
        public static EntityDestroyedEvent CreateEntityDestroyedEvent(Guid entityId, string entityName)
        {
            return new EntityDestroyedEvent(entityId, entityName);
        }

        /// <summary>
        /// Create a component added event
        /// </summary>
        public static ComponentAddedEvent CreateComponentAddedEvent(Guid entityId, Type componentType, object component)
        {
            return new ComponentAddedEvent(entityId, componentType, component);
        }

        /// <summary>
        /// Create a component removed event
        /// </summary>
        public static ComponentRemovedEvent CreateComponentRemovedEvent(Guid entityId, Type componentType)
        {
            return new ComponentRemovedEvent(entityId, componentType);
        }

        /// <summary>
        /// Create a collision enter event
        /// </summary>
        public static CollisionEnterEvent CreateCollisionEnterEvent(Guid entityAId, Guid entityBId, 
            Vector2 collisionPoint, Rectangle intersectionArea, string collisionTypeA, string collisionTypeB)
        {
            return new CollisionEnterEvent(entityAId, entityBId, collisionPoint, intersectionArea, collisionTypeA, collisionTypeB);
        }

        /// <summary>
        /// Create a health changed event
        /// </summary>
        public static HealthChangedEvent CreateHealthChangedEvent(Guid entityId, int previousHealth, 
            int currentHealth, int maxHealth, string changeReason = "")
        {
            return new HealthChangedEvent(entityId, previousHealth, currentHealth, maxHealth, changeReason);
        }

        /// <summary>
        /// Create an entity death event
        /// </summary>
        public static EntityDeathEvent CreateEntityDeathEvent(Guid entityId, string entityName, 
            Guid? killerId = null, string deathCause = "")
        {
            return new EntityDeathEvent(entityId, entityName, killerId, deathCause);
        }

        /// <summary>
        /// Create a damage dealt event
        /// </summary>
        public static DamageDealtEvent CreateDamageDealtEvent(Guid attackerId, Guid targetId, 
            int damageAmount, string damageType = "Physical", bool isCritical = false)
        {
            return new DamageDealtEvent(attackerId, targetId, damageAmount, damageType, isCritical);
        }

        /// <summary>
        /// Create a turn started event
        /// </summary>
        public static TurnStartedEvent CreateTurnStartedEvent(Guid entityId, int turnNumber, int roundNumber)
        {
            return new TurnStartedEvent(entityId, turnNumber, roundNumber);
        }

        /// <summary>
        /// Create a turn ended event
        /// </summary>
        public static TurnEndedEvent CreateTurnEndedEvent(Guid entityId, int actionsUsed, bool turnSkipped = false)
        {
            return new TurnEndedEvent(entityId, actionsUsed, turnSkipped);
        }

        /// <summary>
        /// Create an ability used event
        /// </summary>
        public static AbilityUsedEvent CreateAbilityUsedEvent(Guid casterId, string abilityId, string abilityName,
            Guid[] targetIds, int manaCost, bool wasSuccessful = true)
        {
            return new AbilityUsedEvent(casterId, abilityId, abilityName, targetIds, manaCost, wasSuccessful);
        }

        /// <summary>
        /// Create an entity moved event
        /// </summary>
        public static EntityMovedEvent CreateEntityMovedEvent(Guid entityId, Vector2 previousPosition, 
            Vector2 newPosition, Vector2 velocity)
        {
            return new EntityMovedEvent(entityId, previousPosition, newPosition, velocity);
        }

        /// <summary>
        /// Create an input action event
        /// </summary>
        public static InputActionEvent CreateInputActionEvent(string actionName, Guid? playerId = null, object actionData = null)
        {
            return new InputActionEvent(actionName, playerId, actionData);
        }
    }

    /// <summary>
    /// Extension methods for easier event system integration
    /// </summary>
    public static class EventSystemExtensions
    {
        /// <summary>
        /// Subscribe to multiple event types at once
        /// </summary>
        public static List<IEventSubscription> SubscribeToMultiple(this EventSystem eventSystem, 
            params (Type eventType, Delegate handler)[] subscriptions)
        {
            var results = new List<IEventSubscription>();
            
            foreach (var (eventType, handler) in subscriptions)
            {
                // Use reflection to call the generic Subscribe method
                var subscribeMethod = typeof(EventSystem).GetMethod("Subscribe").MakeGenericMethod(eventType);
                var subscription = (IEventSubscription)subscribeMethod.Invoke(eventSystem, new object[] { handler });
                results.Add(subscription);
            }
            
            return results;
        }

        /// <summary>
        /// Queue multiple events at once
        /// </summary>
        public static int QueueMultipleEvents(this EventSystem eventSystem, params IEvent[] events)
        {
            int successCount = 0;
            foreach (var eventToQueue in events)
            {
                if (eventSystem.QueueEvent(eventToQueue))
                {
                    successCount++;
                }
            }
            return successCount;
        }

        /// <summary>
        /// Subscribe to all common game events with a single handler
        /// </summary>
        public static SystemEventSubscriptions SubscribeToCommonEvents(this EventSystem eventSystem,
            Action<IEvent> commonHandler)
        {
            var subscriptions = new SystemEventSubscriptions(eventSystem);
            
            // Subscribe to common event types
            subscriptions.Subscribe<EntityCreatedEvent>(e => commonHandler(e));
            subscriptions.Subscribe<EntityDestroyedEvent>(e => commonHandler(e));
            subscriptions.Subscribe<CollisionEnterEvent>(e => commonHandler(e));
            subscriptions.Subscribe<CollisionExitEvent>(e => commonHandler(e));
            subscriptions.Subscribe<HealthChangedEvent>(e => commonHandler(e));
            subscriptions.Subscribe<EntityDeathEvent>(e => commonHandler(e));
            subscriptions.Subscribe<TurnStartedEvent>(e => commonHandler(e));
            subscriptions.Subscribe<TurnEndedEvent>(e => commonHandler(e));
            subscriptions.Subscribe<AbilityUsedEvent>(e => commonHandler(e));
            subscriptions.Subscribe<EntityMovedEvent>(e => commonHandler(e));
            
            return subscriptions;
        }
    }

    /// <summary>
    /// Event aggregator for collecting and batching similar events
    /// </summary>
    public class EventAggregator : IDisposable
    {
        private readonly EventSystem _eventSystem;
        private readonly Dictionary<Type, List<IEvent>> _aggregatedEvents;
        private readonly TimeSpan _batchInterval;
        private DateTime _lastBatchTime;
        private bool _disposed;

        public EventAggregator(EventSystem eventSystem, TimeSpan batchInterval)
        {
            _eventSystem = eventSystem ?? throw new ArgumentNullException(nameof(eventSystem));
            _aggregatedEvents = new Dictionary<Type, List<IEvent>>();
            _batchInterval = batchInterval;
            _lastBatchTime = DateTime.UtcNow;
        }

        /// <summary>
        /// Add an event to be aggregated
        /// </summary>
        public void AggregateEvent(IEvent eventToAggregate)
        {
            if (_disposed || eventToAggregate == null)
                return;

            var eventType = eventToAggregate.GetType();
            
            if (!_aggregatedEvents.ContainsKey(eventType))
            {
                _aggregatedEvents[eventType] = new List<IEvent>();
            }
            
            _aggregatedEvents[eventType].Add(eventToAggregate);
        }

        /// <summary>
        /// Process aggregated events if the batch interval has elapsed
        /// </summary>
        public void Update(GameTime gameTime)
        {
            if (_disposed)
                return;

            if (DateTime.UtcNow - _lastBatchTime >= _batchInterval)
            {
                FlushAggregatedEvents();
                _lastBatchTime = DateTime.UtcNow;
            }
        }

        /// <summary>
        /// Immediately flush all aggregated events
        /// </summary>
        public void FlushAggregatedEvents()
        {
            if (_disposed)
                return;

            foreach (var kvp in _aggregatedEvents)
            {
                foreach (var aggregatedEvent in kvp.Value)
                {
                    _eventSystem.QueueEvent(aggregatedEvent);
                }
            }
            
            _aggregatedEvents.Clear();
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                FlushAggregatedEvents();
                _disposed = true;
            }
        }
    }
}
