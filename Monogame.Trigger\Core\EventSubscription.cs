using System;
using System.Collections.Generic;
using System.Linq;

namespace Monogame.Trigger.Core
{
    /// <summary>
    /// Interface for event subscriptions
    /// </summary>
    public interface IEventSubscription : IDisposable
    {
        /// <summary>
        /// The type of event this subscription handles
        /// </summary>
        Type EventType { get; }

        /// <summary>
        /// Whether this subscription is still active
        /// </summary>
        bool IsActive { get; }

        /// <summary>
        /// Invoke the subscription handler with the given event
        /// </summary>
        /// <param name="eventData">The event to handle</param>
        void Invoke(IEvent eventData);

        /// <summary>
        /// Unsubscribe from the event manager
        /// </summary>
        void Unsubscribe();
    }

    /// <summary>
    /// Generic event subscription implementation
    /// </summary>
    /// <typeparam name="T">The event type</typeparam>
    internal class EventSubscription<T> : IEventSubscription where T : class, IEvent
    {
        private readonly Action<T> _handler;
        private readonly EventManager _eventManager;
        private bool _isActive;

        public Type EventType => typeof(T);
        public bool IsActive => _isActive;

        public EventSubscription(Action<T> handler, EventManager eventManager)
        {
            _handler = handler ?? throw new ArgumentNullException(nameof(handler));
            _eventManager = eventManager ?? throw new ArgumentNullException(nameof(eventManager));
            _isActive = true;
        }

        public void Invoke(IEvent eventData)
        {
            if (!_isActive || eventData == null)
                return;

            if (eventData is T typedEvent)
            {
                _handler(typedEvent);
            }
        }

        public void Unsubscribe()
        {
            if (_isActive)
            {
                _isActive = false;
                _eventManager.Unsubscribe(this);
            }
        }

        public void Dispose()
        {
            Unsubscribe();
        }
    }

    /// <summary>
    /// Helper class for managing multiple subscriptions
    /// </summary>
    public class EventSubscriptionManager : IDisposable
    {
        private readonly List<IEventSubscription> _subscriptions;
        private bool _disposed;

        public int SubscriptionCount => _subscriptions.Count;

        public EventSubscriptionManager()
        {
            _subscriptions = new List<IEventSubscription>();
        }

        /// <summary>
        /// Add a subscription to be managed
        /// </summary>
        /// <param name="subscription">The subscription to manage</param>
        public void AddSubscription(IEventSubscription subscription)
        {
            if (subscription != null && !_disposed)
            {
                _subscriptions.Add(subscription);
            }
        }

        /// <summary>
        /// Subscribe to an event and add it to the managed subscriptions
        /// </summary>
        /// <typeparam name="T">The event type</typeparam>
        /// <param name="eventManager">The event manager to subscribe to</param>
        /// <param name="handler">The event handler</param>
        /// <returns>The created subscription</returns>
        public IEventSubscription Subscribe<T>(EventManager eventManager, Action<T> handler) where T : class, IEvent
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(EventSubscriptionManager));

            var subscription = eventManager.Subscribe(handler);
            AddSubscription(subscription);
            return subscription;
        }

        /// <summary>
        /// Remove a specific subscription
        /// </summary>
        /// <param name="subscription">The subscription to remove</param>
        public void RemoveSubscription(IEventSubscription subscription)
        {
            if (subscription != null)
            {
                _subscriptions.Remove(subscription);
                subscription.Dispose();
            }
        }

        /// <summary>
        /// Unsubscribe from all managed subscriptions
        /// </summary>
        public void UnsubscribeAll()
        {
            foreach (var subscription in _subscriptions.ToList())
            {
                subscription.Dispose();
            }
            _subscriptions.Clear();
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                UnsubscribeAll();
                _disposed = true;
            }
        }
    }
}
