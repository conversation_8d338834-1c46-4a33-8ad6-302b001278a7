﻿// Updated File: EntityManager.cs
using Monogame.ECS.Core.BaseClasses;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Monogame.ECS.Core
{
    public class EntityManager
    {
        private readonly List<Entity> _entities = new List<Entity>();
        private readonly Queue<Entity> _entitiesToAdd = new Queue<Entity>();
        private readonly Queue<Entity> _entitiesToRemove = new Queue<Entity>();

        public void AddEntity(Entity entity)
        {
            _entitiesToAdd.Enqueue(entity);
        }

        public void RemoveEntity(Entity entity)
        {
            _entitiesToRemove.Enqueue(entity);
        }

        public Entity GetEntity(Guid id)
        {
            return _entities.FirstOrDefault(e => e.Id == id);
        }

        public Entity GetEntity(string name)
        {
            return _entities.FirstOrDefault(e => e.Name == name);
        }

        public IEnumerable<Entity> GetEntities()
        {
            return _entities.Where(e => e.IsActive);
        }

        public IEnumerable<Entity> GetEntitiesWithComponent<T>() where T : Component
        {
            return _entities.Where(e => e.IsActive && e.HasComponent<T>());
        }

        public IEnumerable<Entity> GetEntitiesWithComponents(params Type[] componentTypes)
        {
            return _entities.Where(e => e.IsActive && e.HasComponents(componentTypes));
        }

        public void Update()
        {
            // Add pending entities
            while (_entitiesToAdd.Count > 0)
            {
                _entities.Add(_entitiesToAdd.Dequeue());
            }

            // Remove pending entities
            while (_entitiesToRemove.Count > 0)
            {
                var entity = _entitiesToRemove.Dequeue();
                _entities.Remove(entity);
            }
        }
    }
}