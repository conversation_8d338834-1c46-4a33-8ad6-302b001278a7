using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;
using Monogame.ECS.Core;
using Monogame.ECS.Core.BaseClasses;
using Monogame.ECS.Components;

namespace Monogame.ECS.System
{
    public class RenderSystem : ISystem
    {
        private readonly EntityManager _entityManager;
        private SpriteBatch _spriteBatch;
        private GraphicsDevice _graphicsDevice;

        public IEnumerable<Type> RequiredComponentTypes => new[]
        {
            typeof(TransformComponent),
            typeof(SpriteComponent)
        };

        public RenderSystem(EntityManager entityManager)
        {
            _entityManager = entityManager;
        }

        public void Initialize()
        {
            // SpriteBatch and GraphicsDevice will be set externally
        }

        public void SetGraphicsResources(SpriteBatch spriteBatch, GraphicsDevice graphicsDevice)
        {
            _spriteBatch = spriteBatch;
            _graphicsDevice = graphicsDevice;
        }

        public void Update(GameTime gameTime)
        {
            // This system doesn't update during the game loop
            // Rendering is handled by the Draw method
        }

        public void Draw(GameTime gameTime)
        {
            if (_spriteBatch == null)
                return;

            var renderableEntities = _entityManager.GetEntitiesWithComponents(
                typeof(TransformComponent), 
                typeof(SpriteComponent))
                .Where(e => e.GetComponent<SpriteComponent>().IsVisible)
                .OrderBy(e => e.GetComponent<SpriteComponent>().LayerDepth);

            _spriteBatch.Begin(SpriteSortMode.BackToFront, BlendState.AlphaBlend);

            foreach (var entity in renderableEntities)
            {
                var transform = entity.GetComponent<TransformComponent>();
                var sprite = entity.GetComponent<SpriteComponent>();

                if (sprite.Texture == null)
                    continue;

                // Handle animation if present
                var animation = entity.GetComponent<AnimationComponent>();
                Rectangle? sourceRect = sprite.SourceRectangle;
                
                if (animation != null && animation.CurrentFrameRectangle.HasValue)
                {
                    sourceRect = animation.CurrentFrameRectangle.Value;
                }

                _spriteBatch.Draw(
                    sprite.Texture,
                    transform.Position,
                    sourceRect,
                    sprite.Color,
                    transform.Rotation,
                    transform.Origin,
                    transform.Scale,
                    sprite.Effects,
                    sprite.LayerDepth
                );
            }

            _spriteBatch.End();
        }

        public void DrawUI(GameTime gameTime)
        {
            if (_spriteBatch == null)
                return;

            // Draw UI elements like health bars, etc.
            var entitiesWithHealth = _entityManager.GetEntitiesWithComponents(
                typeof(TransformComponent),
                typeof(HealthComponent))
                .Where(e => e.HasComponent<SpriteComponent>() && 
                           e.GetComponent<SpriteComponent>().IsVisible);

            _spriteBatch.Begin();

            foreach (var entity in entitiesWithHealth)
            {
                var transform = entity.GetComponent<TransformComponent>();
                var health = entity.GetComponent<HealthComponent>();
                
                DrawHealthBar(transform.Position, health);
            }

            _spriteBatch.End();
        }

        private void DrawHealthBar(Vector2 position, HealthComponent health)
        {
            if (health.HealthPercentage >= 1.0f)
                return; // Don't show health bar if at full health

            var barWidth = 40;
            var barHeight = 6;
            var barPosition = new Vector2(position.X - barWidth / 2, position.Y - 30);

            // Background (red)
            var backgroundRect = new Rectangle((int)barPosition.X, (int)barPosition.Y, barWidth, barHeight);
            DrawRectangle(backgroundRect, Color.Red);

            // Foreground (green)
            var foregroundWidth = (int)(barWidth * health.HealthPercentage);
            var foregroundRect = new Rectangle((int)barPosition.X, (int)barPosition.Y, foregroundWidth, barHeight);
            DrawRectangle(foregroundRect, Color.Green);
        }

        private void DrawRectangle(Rectangle rectangle, Color color)
        {
            // Create a 1x1 white pixel texture if not available
            // This is a simple implementation - in practice you'd want to cache this
            var pixel = new Texture2D(_graphicsDevice, 1, 1);
            pixel.SetData(new[] { Color.White });

            _spriteBatch.Draw(pixel, rectangle, color);
        }
    }
}
