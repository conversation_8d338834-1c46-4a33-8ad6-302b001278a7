﻿// Updated File: Entity.cs
using System;
using System.Collections.Generic;
using System.Linq;

namespace Monogame.ECS.Core.BaseClasses
{
    public class Entity
    {
        public string Name { get; set; }
        public Guid Id { get; private set; }
        public bool IsActive { get; set; } = true;

        private readonly Dictionary<Type, Component> _components;

        public Entity(string name)
        {
            Name = name;
            Id = Guid.NewGuid();
            _components = new Dictionary<Type, Component>();
        }

        public void AddComponent<T>(T component) where T : Component
        {
            _components[typeof(T)] = component;
        }

        public void RemoveComponent<T>() where T : Component
        {
            _components.Remove(typeof(T));
        }

        public T GetComponent<T>() where T : Component
        {
            return _components.TryGetValue(typeof(T), out var component)
                ? (T)component : null;
        }

        // Generic version (existing)
        public bool HasComponent<T>() where T : Component
        {
            return _components.ContainsKey(typeof(T));
        }

        // Non-generic version (new)
        public bool HasComponent(Type componentType)
        {
            return _components.ContainsKey(componentType);
        }

        // Check multiple components by Type array (new)
        public bool HasComponents(params Type[] componentTypes)
        {
            return componentTypes.All(type => _components.ContainsKey(type));
        }

        public IEnumerable<Component> GetAllComponents()
        {
            return _components.Values;
        }
    }
}