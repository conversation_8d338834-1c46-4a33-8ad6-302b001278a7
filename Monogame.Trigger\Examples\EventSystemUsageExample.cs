using System;
using Microsoft.Xna.Framework;
using Monogame.Trigger.Core;
using Monogame.Trigger.Events;
using Monogame.Trigger.System;
using Monogame.Trigger.Integration;

namespace Monogame.Trigger.Examples
{
    /// <summary>
    /// Example demonstrating how to use the Event System with the MonoGame ECS architecture
    /// </summary>
    public class EventSystemUsageExample
    {
        private EventSystem _eventSystem;
        private SystemEventSubscriptions _subscriptions;

        public void Initialize()
        {
            // Create the event system
            _eventSystem = new EventSystem(maxQueueSize: 1000, maxEventsPerFrame: 50);
            _eventSystem.Initialize();

            // Create subscription manager for this system
            _subscriptions = new SystemEventSubscriptions(_eventSystem);

            // Subscribe to various events
            SetupEventSubscriptions();
        }

        private void SetupEventSubscriptions()
        {
            // Subscribe to entity lifecycle events
            _subscriptions.Subscribe<EntityCreatedEvent>(OnEntityCreated);
            _subscriptions.Subscribe<EntityDestroyedEvent>(OnEntityDestroyed);
            _subscriptions.Subscribe<ComponentAddedEvent>(OnComponentAdded);

            // Subscribe to collision events
            _subscriptions.Subscribe<CollisionEnterEvent>(OnCollisionEnter);
            _subscriptions.Subscribe<CollisionExitEvent>(OnCollisionExit);

            // Subscribe to health and combat events
            _subscriptions.Subscribe<HealthChangedEvent>(OnHealthChanged);
            _subscriptions.Subscribe<EntityDeathEvent>(OnEntityDeath);
            _subscriptions.Subscribe<DamageDealtEvent>(OnDamageDealt);

            // Subscribe to turn-based events
            _subscriptions.Subscribe<TurnStartedEvent>(OnTurnStarted);
            _subscriptions.Subscribe<TurnEndedEvent>(OnTurnEnded);
            _subscriptions.Subscribe<RoundStartedEvent>(OnRoundStarted);

            // Subscribe to ability events
            _subscriptions.Subscribe<AbilityUsedEvent>(OnAbilityUsed);
            _subscriptions.Subscribe<ManaChangedEvent>(OnManaChanged);

            // Subscribe to movement events
            _subscriptions.Subscribe<EntityMovedEvent>(OnEntityMoved);

            // Subscribe to input events
            _subscriptions.Subscribe<InputActionEvent>(OnInputAction);
        }

        public void Update(GameTime gameTime)
        {
            // Process events during the update loop
            _eventSystem.Update(gameTime);
        }

        // Event handlers
        private void OnEntityCreated(EntityCreatedEvent eventData)
        {
            Console.WriteLine($"Entity created: {eventData.EntityName} ({eventData.EntityId})");
            Console.WriteLine($"Components: {string.Join(", ", Array.ConvertAll(eventData.ComponentTypes, t => t.Name))}");
        }

        private void OnEntityDestroyed(EntityDestroyedEvent eventData)
        {
            Console.WriteLine($"Entity destroyed: {eventData.EntityName} ({eventData.EntityId})");
        }

        private void OnComponentAdded(ComponentAddedEvent eventData)
        {
            Console.WriteLine($"Component {eventData.ComponentType.Name} added to entity {eventData.EntityId}");
        }

        private void OnCollisionEnter(CollisionEnterEvent eventData)
        {
            Console.WriteLine($"Collision between {eventData.EntityAId} and {eventData.EntityBId}");
            Console.WriteLine($"Types: {eventData.CollisionTypeA} vs {eventData.CollisionTypeB}");
            
            // Example: Handle item pickup
            if (eventData.CollisionTypeA == "Player" && eventData.CollisionTypeB == "Item")
            {
                // Queue an item pickup event
                var itemPickupEvent = new InputActionEvent("ItemPickup", eventData.EntityAId, eventData.EntityBId);
                _subscriptions.QueueEvent(itemPickupEvent);
            }
        }

        private void OnCollisionExit(CollisionExitEvent eventData)
        {
            Console.WriteLine($"Collision ended between {eventData.EntityAId} and {eventData.EntityBId}");
        }

        private void OnHealthChanged(HealthChangedEvent eventData)
        {
            Console.WriteLine($"Entity {eventData.EntityId} health: {eventData.PreviousHealth} -> {eventData.CurrentHealth}");
            Console.WriteLine($"Reason: {eventData.ChangeReason}");

            // Check for death
            if (eventData.CurrentHealth <= 0 && eventData.PreviousHealth > 0)
            {
                var deathEvent = ECSEventIntegration.CreateEntityDeathEvent(
                    eventData.EntityId, 
                    "Unknown Entity", 
                    null, 
                    eventData.ChangeReason);
                _subscriptions.QueueEvent(deathEvent);
            }
        }

        private void OnEntityDeath(EntityDeathEvent eventData)
        {
            Console.WriteLine($"Entity {eventData.EntityName} has died!");
            if (eventData.KillerId.HasValue)
            {
                Console.WriteLine($"Killed by: {eventData.KillerId}");
            }
            Console.WriteLine($"Cause: {eventData.DeathCause}");
        }

        private void OnDamageDealt(DamageDealtEvent eventData)
        {
            var critText = eventData.IsCritical ? " (CRITICAL!)" : "";
            Console.WriteLine($"Damage dealt: {eventData.DamageAmount} {eventData.DamageType} damage{critText}");
            Console.WriteLine($"From {eventData.AttackerId} to {eventData.TargetId}");
        }

        private void OnTurnStarted(TurnStartedEvent eventData)
        {
            Console.WriteLine($"Turn started for entity {eventData.EntityId}");
            Console.WriteLine($"Turn {eventData.TurnNumber}, Round {eventData.RoundNumber}");
        }

        private void OnTurnEnded(TurnEndedEvent eventData)
        {
            Console.WriteLine($"Turn ended for entity {eventData.EntityId}");
            Console.WriteLine($"Actions used: {eventData.ActionsUsed}");
            if (eventData.TurnSkipped)
            {
                Console.WriteLine("Turn was skipped");
            }
        }

        private void OnRoundStarted(RoundStartedEvent eventData)
        {
            Console.WriteLine($"Round {eventData.RoundNumber} started with {eventData.ParticipantIds.Length} participants");
        }

        private void OnAbilityUsed(AbilityUsedEvent eventData)
        {
            Console.WriteLine($"Ability used: {eventData.AbilityName} by {eventData.CasterId}");
            Console.WriteLine($"Targets: {string.Join(", ", eventData.TargetIds)}");
            Console.WriteLine($"Mana cost: {eventData.ManaCost}");
            Console.WriteLine($"Success: {eventData.WasSuccessful}");
        }

        private void OnManaChanged(ManaChangedEvent eventData)
        {
            Console.WriteLine($"Entity {eventData.EntityId} mana: {eventData.PreviousMana} -> {eventData.CurrentMana}/{eventData.MaxMana}");
        }

        private void OnEntityMoved(EntityMovedEvent eventData)
        {
            // Only log significant movements to avoid spam
            var distance = Vector2.Distance(eventData.PreviousPosition, eventData.NewPosition);
            if (distance > 10f)
            {
                Console.WriteLine($"Entity {eventData.EntityId} moved from {eventData.PreviousPosition} to {eventData.NewPosition}");
            }
        }

        private void OnInputAction(InputActionEvent eventData)
        {
            Console.WriteLine($"Input action: {eventData.ActionName}");
            if (eventData.PlayerId.HasValue)
            {
                Console.WriteLine($"Player: {eventData.PlayerId}");
            }
        }

        // Example of firing events
        public void ExampleFireEvents()
        {
            // Fire an immediate event
            var immediateEvent = new InputActionEvent("PlayerAttack", Guid.NewGuid());
            _eventSystem.FireEventImmediate(immediateEvent);

            // Queue events for later processing
            var entityId = Guid.NewGuid();
            var createdEvent = ECSEventIntegration.CreateEntityCreatedEvent(
                entityId, 
                "Player", 
                new Type[] { typeof(object) }); // Replace with actual component types
            _subscriptions.QueueEvent(createdEvent);

            var healthEvent = ECSEventIntegration.CreateHealthChangedEvent(
                entityId, 
                100, 
                75, 
                100, 
                "Damage taken");
            _subscriptions.QueueEvent(healthEvent);

            // Queue multiple events at once
            var events = new IEvent[]
            {
                ECSEventIntegration.CreateTurnStartedEvent(entityId, 1, 1),
                ECSEventIntegration.CreateAbilityUsedEvent(entityId, "fireball", "Fireball", new Guid[0], 25),
                ECSEventIntegration.CreateTurnEndedEvent(entityId, 1)
            };
            _eventSystem.QueueMultipleEvents(events);
        }

        public void Cleanup()
        {
            // Clean up subscriptions
            _subscriptions?.Dispose();
            
            // Clear the event system
            _eventSystem?.Clear();
        }

        // Example of creating a custom event
        public class CustomGameEvent : BaseEvent
        {
            public string Message { get; }
            public Guid PlayerId { get; }

            public CustomGameEvent(string message, Guid playerId)
                : base(priority: 1, source: "CustomSystem")
            {
                Message = message;
                PlayerId = playerId;
            }
        }

        // Example of subscribing to and firing custom events
        public void CustomEventExample()
        {
            // Subscribe to custom event
            _subscriptions.Subscribe<CustomGameEvent>(OnCustomEvent);

            // Fire custom event
            var customEvent = new CustomGameEvent("Hello from custom event!", Guid.NewGuid());
            _subscriptions.QueueEvent(customEvent);
        }

        private void OnCustomEvent(CustomGameEvent eventData)
        {
            Console.WriteLine($"Custom event received: {eventData.Message} from player {eventData.PlayerId}");
        }
    }
}
