using Microsoft.Xna.Framework;
using Monogame.ECS.Core.BaseClasses;

namespace Monogame.ECS.Components
{
    public enum CollisionType
    {
        None,
        Solid,
        Trigger,
        Player,
        Enemy,
        Projectile,
        Item,
        Wall
    }

    public class CollisionComponent : Component
    {
        public Rectangle Bounds { get; set; }
        public Vector2 Offset { get; set; }
        public CollisionType Type { get; set; }
        public bool IsEnabled { get; set; }
        public bool IsTrigger { get; set; }

        public CollisionComponent()
        {
            Bounds = Rectangle.Empty;
            Offset = Vector2.Zero;
            Type = CollisionType.None;
            IsEnabled = true;
            IsTrigger = false;
        }

        public CollisionComponent(Rectangle bounds, CollisionType type = CollisionType.Solid) : this()
        {
            Bounds = bounds;
            Type = type;
        }

        public CollisionComponent(int width, int height, CollisionType type = CollisionType.Solid) : this()
        {
            Bounds = new Rectangle(0, 0, width, height);
            Type = type;
        }

        public Rectangle GetWorldBounds(Vector2 position)
        {
            return new Rectangle(
                (int)(position.X + Offset.X),
                (int)(position.Y + Offset.Y),
                Bounds.Width,
                Bounds.Height
            );
        }

        public bool Intersects(CollisionComponent other, Vector2 thisPosition, Vector2 otherPosition)
        {
            if (!IsEnabled || !other.IsEnabled)
                return false;

            var thisBounds = GetWorldBounds(thisPosition);
            var otherBounds = other.GetWorldBounds(otherPosition);
            
            return thisBounds.Intersects(otherBounds);
        }

        public bool Contains(Vector2 point, Vector2 position)
        {
            if (!IsEnabled)
                return false;

            var worldBounds = GetWorldBounds(position);
            return worldBounds.Contains(point);
        }
    }
}
