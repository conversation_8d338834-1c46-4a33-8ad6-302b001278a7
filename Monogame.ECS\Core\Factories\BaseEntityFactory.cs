using Monogame.ECS.Core.BaseClasses;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Monogame.ECS.Core.Factories
{
    public abstract class BaseEntityFactory : IEntityFactory
    {
        public abstract string FactoryName { get; }

        public abstract Entity CreateEntity();

        // Helper method to configure entities
        protected virtual void ConfigureEntity(Entity entity)
        {
            // Override this in derived classes to add components
        }
    }
}
