using System;
using System.Collections.Generic;
using Microsoft.Xna.Framework;
using Monogame.ECS.Core.BaseClasses;

namespace Monogame.ECS.Components
{
    public class Animation
    {
        public string Name { get; set; }
        public List<Rectangle> Frames { get; set; }
        public float FrameDuration { get; set; }
        public bool IsLooping { get; set; }

        public Animation(string name, List<Rectangle> frames, float frameDuration, bool isLooping = true)
        {
            Name = name;
            Frames = frames ?? new List<Rectangle>();
            FrameDuration = frameDuration;
            IsLooping = isLooping;
        }
    }

    public class AnimationComponent : Component
    {
        public Dictionary<string, Animation> Animations { get; set; }
        public string CurrentAnimationName { get; set; }
        public int CurrentFrame { get; set; }
        public float ElapsedTime { get; set; }
        public bool IsPlaying { get; set; }
        public bool IsFinished { get; set; }

        public Animation CurrentAnimation => 
            !string.IsNullOrEmpty(CurrentAnimationName) && Animations.ContainsKey(CurrentAnimationName) 
                ? Animations[CurrentAnimationName] 
                : null;

        public Rectangle? CurrentFrameRectangle => 
            CurrentAnimation != null && CurrentFrame < CurrentAnimation.Frames.Count 
                ? CurrentAnimation.Frames[CurrentFrame] 
                : null;

        public AnimationComponent()
        {
            Animations = new Dictionary<string, Animation>();
            CurrentFrame = 0;
            ElapsedTime = 0f;
            IsPlaying = false;
            IsFinished = false;
        }

        public void AddAnimation(Animation animation)
        {
            Animations[animation.Name] = animation;
        }

        public void PlayAnimation(string animationName, bool restart = false)
        {
            if (Animations.ContainsKey(animationName))
            {
                if (CurrentAnimationName != animationName || restart)
                {
                    CurrentAnimationName = animationName;
                    CurrentFrame = 0;
                    ElapsedTime = 0f;
                    IsFinished = false;
                }
                IsPlaying = true;
            }
        }

        public void StopAnimation()
        {
            IsPlaying = false;
        }

        public void PauseAnimation()
        {
            IsPlaying = false;
        }

        public void ResumeAnimation()
        {
            if (CurrentAnimation != null)
            {
                IsPlaying = true;
            }
        }

        public void Update(GameTime gameTime)
        {
            if (!IsPlaying || CurrentAnimation == null || IsFinished)
                return;

            ElapsedTime += (float)gameTime.ElapsedGameTime.TotalSeconds;

            if (ElapsedTime >= CurrentAnimation.FrameDuration)
            {
                ElapsedTime = 0f;
                CurrentFrame++;

                if (CurrentFrame >= CurrentAnimation.Frames.Count)
                {
                    if (CurrentAnimation.IsLooping)
                    {
                        CurrentFrame = 0;
                    }
                    else
                    {
                        CurrentFrame = CurrentAnimation.Frames.Count - 1;
                        IsFinished = true;
                        IsPlaying = false;
                    }
                }
            }
        }
    }
}
