using Microsoft.Xna.Framework;
using Monogame.ECS.Core.BaseClasses;

namespace Monogame.ECS.Components
{
    public class TransformComponent : Component
    {
        public Vector2 Position { get; set; }
        public float Rotation { get; set; }
        public Vector2 Scale { get; set; }
        public Vector2 Origin { get; set; }

        public TransformComponent()
        {
            Position = Vector2.Zero;
            Rotation = 0f;
            Scale = Vector2.One;
            Origin = Vector2.Zero;
        }

        public TransformComponent(Vector2 position) : this()
        {
            Position = position;
        }

        public TransformComponent(Vector2 position, float rotation, Vector2 scale) : this()
        {
            Position = position;
            Rotation = rotation;
            Scale = scale;
        }
    }
}
