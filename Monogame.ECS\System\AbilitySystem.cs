using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Xna.Framework;
using Monogame.ECS.Core;
using Monogame.ECS.Core.BaseClasses;
using Monogame.ECS.Components;

namespace Monogame.ECS.System
{
    public class AbilitySystem : ISystem
    {
        private readonly EntityManager _entityManager;

        public IEnumerable<Type> RequiredComponentTypes => new[]
        {
            typeof(AbilityComponent)
        };

        // Events for ability usage
        public event Action<Entity, Ability> OnAbilityUsed;
        public event Action<Entity, Ability, Entity> OnAbilityTargeted;
        public event Action<Entity, int> OnManaChanged;

        public AbilitySystem(EntityManager entityManager)
        {
            _entityManager = entityManager;
        }

        public void Initialize()
        {
            // No initialization needed
        }

        public void Update(GameTime gameTime)
        {
            var entitiesWithAbilities = _entityManager.GetEntitiesWithComponent<AbilityComponent>();
            float deltaTime = (float)gameTime.ElapsedGameTime.TotalSeconds;

            foreach (var entity in entitiesWithAbilities)
            {
                var abilities = entity.GetComponent<AbilityComponent>();
                
                // Update cooldowns
                abilities.UpdateCooldowns(deltaTime);
                
                // Regenerate mana
                int previousMana = abilities.CurrentMana;
                abilities.RegenerateMana(deltaTime);
                
                if (abilities.CurrentMana != previousMana)
                {
                    OnManaChanged?.Invoke(entity, abilities.CurrentMana);
                }
            }
        }

        public bool UseAbility(Entity caster, string abilityId, Entity target = null)
        {
            var abilities = caster.GetComponent<AbilityComponent>();
            if (abilities == null)
                return false;

            var ability = abilities.GetAbility(abilityId);
            if (ability == null || !abilities.CanUseAbility(abilityId))
                return false;

            // Validate target based on ability type
            if (!IsValidTarget(ability, caster, target))
                return false;

            // Use the ability
            if (!abilities.UseAbility(abilityId))
                return false;

            // Apply ability effects
            ApplyAbilityEffects(caster, ability, target);

            // Fire events
            OnAbilityUsed?.Invoke(caster, ability);
            if (target != null)
            {
                OnAbilityTargeted?.Invoke(caster, ability, target);
            }

            return true;
        }

        private bool IsValidTarget(Ability ability, Entity caster, Entity target)
        {
            switch (ability.TargetType)
            {
                case TargetType.Self:
                    return target == null || target.Id == caster.Id;

                case TargetType.SingleEnemy:
                    return target != null && IsEnemy(caster, target) && IsInRange(caster, target, ability.Range);

                case TargetType.SingleAlly:
                    return target != null && IsAlly(caster, target) && IsInRange(caster, target, ability.Range);

                case TargetType.AllEnemies:
                case TargetType.AllAllies:
                case TargetType.Area:
                    return true; // These don't require specific target validation

                default:
                    return false;
            }
        }

        private void ApplyAbilityEffects(Entity caster, Ability ability, Entity target)
        {
            switch (ability.Type)
            {
                case AbilityType.Attack:
                    ApplyAttackAbility(caster, ability, target);
                    break;

                case AbilityType.Heal:
                    ApplyHealAbility(caster, ability, target);
                    break;

                case AbilityType.Buff:
                    ApplyBuffAbility(caster, ability, target);
                    break;

                case AbilityType.Debuff:
                    ApplyDebuffAbility(caster, ability, target);
                    break;

                case AbilityType.Utility:
                    ApplyUtilityAbility(caster, ability, target);
                    break;

                case AbilityType.Movement:
                    ApplyMovementAbility(caster, ability, target);
                    break;
            }
        }

        private void ApplyAttackAbility(Entity caster, Ability ability, Entity target)
        {
            var targets = GetTargets(caster, ability, target);
            var casterStats = caster.GetComponent<StatsComponent>();

            foreach (var targetEntity in targets)
            {
                var targetHealth = targetEntity.GetComponent<HealthComponent>();
                if (targetHealth == null)
                    continue;

                // Calculate damage
                int damage = ability.Damage;
                if (casterStats != null)
                {
                    damage += casterStats.AttackPower / 2; // Add some of caster's attack power
                }

                // Apply damage
                targetHealth.TakeDamage(damage);

                // Trigger animation if available
                var animation = targetEntity.GetComponent<AnimationComponent>();
                if (animation != null)
                {
                    animation.PlayAnimation("hit", true);
                }
            }
        }

        private void ApplyHealAbility(Entity caster, Ability ability, Entity target)
        {
            var targets = GetTargets(caster, ability, target);
            var casterStats = caster.GetComponent<StatsComponent>();

            foreach (var targetEntity in targets)
            {
                var targetHealth = targetEntity.GetComponent<HealthComponent>();
                if (targetHealth == null)
                    continue;

                // Calculate healing
                int healing = ability.Healing;
                if (casterStats != null)
                {
                    healing += casterStats.MagicPower / 3; // Add some of caster's magic power
                }

                // Apply healing
                targetHealth.Heal(healing);
            }
        }

        private void ApplyBuffAbility(Entity caster, Ability ability, Entity target)
        {
            var targets = GetTargets(caster, ability, target);

            foreach (var targetEntity in targets)
            {
                // Apply temporary stat buffs
                // This would typically involve a buff/debuff system
                // For now, just a placeholder
            }
        }

        private void ApplyDebuffAbility(Entity caster, Ability ability, Entity target)
        {
            var targets = GetTargets(caster, ability, target);

            foreach (var targetEntity in targets)
            {
                // Apply temporary stat debuffs
                // This would typically involve a buff/debuff system
                // For now, just a placeholder
            }
        }

        private void ApplyUtilityAbility(Entity caster, Ability ability, Entity target)
        {
            // Handle utility abilities like teleport, invisibility, etc.
            // Implementation depends on specific ability effects
        }

        private void ApplyMovementAbility(Entity caster, Ability ability, Entity target)
        {
            var movement = caster.GetComponent<MovementComponent>();
            if (movement != null)
            {
                // Temporary speed boost or teleportation
                movement.Speed *= 1.5f; // 50% speed boost
            }
        }

        private List<Entity> GetTargets(Entity caster, Ability ability, Entity primaryTarget)
        {
            var targets = new List<Entity>();

            switch (ability.TargetType)
            {
                case TargetType.Self:
                    targets.Add(caster);
                    break;

                case TargetType.SingleEnemy:
                case TargetType.SingleAlly:
                    if (primaryTarget != null)
                        targets.Add(primaryTarget);
                    break;

                case TargetType.AllEnemies:
                    targets.AddRange(GetAllEnemies(caster));
                    break;

                case TargetType.AllAllies:
                    targets.AddRange(GetAllAllies(caster));
                    break;

                case TargetType.Area:
                    targets.AddRange(GetEntitiesInArea(caster, ability.Range));
                    break;
            }

            return targets;
        }

        private List<Entity> GetAllEnemies(Entity caster)
        {
            var enemies = new List<Entity>();
            var isPlayerCaster = caster.HasComponent<PlayerComponent>();

            var allEntities = _entityManager.GetEntities();
            foreach (var entity in allEntities)
            {
                if (entity.Id == caster.Id)
                    continue;

                var isPlayerEntity = entity.HasComponent<PlayerComponent>();
                
                // Players vs non-players are enemies
                if (isPlayerCaster != isPlayerEntity)
                {
                    enemies.Add(entity);
                }
            }

            return enemies;
        }

        private List<Entity> GetAllAllies(Entity caster)
        {
            var allies = new List<Entity>();
            var isPlayerCaster = caster.HasComponent<PlayerComponent>();

            var allEntities = _entityManager.GetEntities();
            foreach (var entity in allEntities)
            {
                if (entity.Id == caster.Id)
                {
                    allies.Add(entity); // Include self
                    continue;
                }

                var isPlayerEntity = entity.HasComponent<PlayerComponent>();
                
                // Same type (player/non-player) are allies
                if (isPlayerCaster == isPlayerEntity)
                {
                    allies.Add(entity);
                }
            }

            return allies;
        }

        private List<Entity> GetEntitiesInArea(Entity caster, float range)
        {
            var entitiesInArea = new List<Entity>();
            var casterTransform = caster.GetComponent<TransformComponent>();
            
            if (casterTransform == null)
                return entitiesInArea;

            var allEntities = _entityManager.GetEntitiesWithComponent<TransformComponent>();
            foreach (var entity in allEntities)
            {
                if (entity.Id == caster.Id)
                    continue;

                var transform = entity.GetComponent<TransformComponent>();
                var distance = Vector2.Distance(casterTransform.Position, transform.Position);
                
                if (distance <= range)
                {
                    entitiesInArea.Add(entity);
                }
            }

            return entitiesInArea;
        }

        private bool IsEnemy(Entity caster, Entity target)
        {
            var isPlayerCaster = caster.HasComponent<PlayerComponent>();
            var isPlayerTarget = target.HasComponent<PlayerComponent>();
            
            return isPlayerCaster != isPlayerTarget;
        }

        private bool IsAlly(Entity caster, Entity target)
        {
            var isPlayerCaster = caster.HasComponent<PlayerComponent>();
            var isPlayerTarget = target.HasComponent<PlayerComponent>();
            
            return isPlayerCaster == isPlayerTarget;
        }

        private bool IsInRange(Entity caster, Entity target, float range)
        {
            var casterTransform = caster.GetComponent<TransformComponent>();
            var targetTransform = target.GetComponent<TransformComponent>();
            
            if (casterTransform == null || targetTransform == null)
                return false;

            var distance = Vector2.Distance(casterTransform.Position, targetTransform.Position);
            return distance <= range;
        }

        public void LearnAbility(Entity entity, Ability ability)
        {
            var abilities = entity.GetComponent<AbilityComponent>();
            abilities?.LearnAbility(ability);
        }

        public void ForgetAbility(Entity entity, string abilityId)
        {
            var abilities = entity.GetComponent<AbilityComponent>();
            abilities?.ForgetAbility(abilityId);
        }

        public List<Ability> GetUsableAbilities(Entity entity)
        {
            var abilities = entity.GetComponent<AbilityComponent>();
            return abilities?.GetUsableAbilities() ?? new List<Ability>();
        }
    }
}
