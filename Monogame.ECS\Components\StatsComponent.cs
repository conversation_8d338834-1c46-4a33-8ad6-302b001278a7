using System;
using System.Collections.Generic;
using Monogame.ECS.Core.BaseClasses;

namespace Monogame.ECS.Components
{
    public class StatsComponent : Component
    {
        public int Strength { get; set; }
        public int Defense { get; set; }
        public int Intelligence { get; set; }
        public int Agility { get; set; }
        public int Luck { get; set; }
        
        // Derived stats
        public int AttackPower => Strength * 2 + 10;
        public int MagicPower => Intelligence * 2 + 5;
        public int PhysicalDefense => Defense * 1.5f + 5;
        public int MagicalDefense => Intelligence + Defense;
        public int CriticalChance => Math.Min(50, Luck / 2); // Max 50% crit chance
        public int Accuracy => 85 + Agility / 4; // Base 85% + agility bonus
        public int Evasion => Math.Min(25, Agility / 3); // Max 25% evasion

        public StatsComponent()
        {
            Strength = 10;
            Defense = 10;
            Intelligence = 10;
            Agility = 10;
            Luck = 10;
        }

        public StatsComponent(int str, int def, int intel, int agi, int luck) : this()
        {
            Strength = str;
            Defense = def;
            Intelligence = intel;
            Agility = agi;
            Luck = luck;
        }

        public void ModifyStat(string statName, int amount)
        {
            switch (statName.ToLower())
            {
                case "strength":
                    Strength = Math.Max(1, Strength + amount);
                    break;
                case "defense":
                    Defense = Math.Max(1, Defense + amount);
                    break;
                case "intelligence":
                    Intelligence = Math.Max(1, Intelligence + amount);
                    break;
                case "agility":
                    Agility = Math.Max(1, Agility + amount);
                    break;
                case "luck":
                    Luck = Math.Max(1, Luck + amount);
                    break;
            }
        }

        public int GetTotalStats()
        {
            return Strength + Defense + Intelligence + Agility + Luck;
        }
    }
}
