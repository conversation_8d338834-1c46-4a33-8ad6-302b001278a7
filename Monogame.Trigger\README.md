# MonoGame ECS Event Queue System

A comprehensive event queue system designed to integrate seamlessly with the existing MonoGame ECS architecture. This system provides a robust, thread-safe, and efficient way to handle game events with priority-based processing and flexible subscription management.

## Features

### Core Event Infrastructure
- **IEvent Interface**: Base interface that all events must implement
- **EventQueue Class**: Thread-safe queue that manages events with priority ordering
- **EventManager/EventBus**: Central hub for event subscription, unsubscription, and dispatching
- **Priority System**: Events can have different priority levels for processing order
- **Immediate vs Queued**: Support for both immediate dispatch and queued processing

### Event Subscription System
- **Generic Type Subscriptions**: Subscribe to specific event types using `Subscribe<T>()`
- **Multiple Subscribers**: Multiple systems can subscribe to the same event type
- **Automatic Cleanup**: Subscription management with proper disposal patterns
- **Delegate/Action Callbacks**: Simple callback-based event handling

### Event Processing
- **Controlled Processing**: Events processed in batches to prevent frame drops
- **Priority Ordering**: Higher priority events processed first
- **Game Loop Integration**: Designed to work within MonoGame's update cycle
- **Thread Safety**: Safe for use across multiple threads

## Quick Start

### 1. Initialize the Event System

```csharp
// Create and initialize the event system
var eventSystem = new EventSystem(maxQueueSize: 1000, maxEventsPerFrame: 50);
eventSystem.Initialize();

// Create subscription manager for your system
var subscriptions = new SystemEventSubscriptions(eventSystem);
```

### 2. Subscribe to Events

```csharp
// Subscribe to specific event types
subscriptions.Subscribe<EntityCreatedEvent>(OnEntityCreated);
subscriptions.Subscribe<CollisionEnterEvent>(OnCollisionEnter);
subscriptions.Subscribe<HealthChangedEvent>(OnHealthChanged);

// Event handlers
private void OnEntityCreated(EntityCreatedEvent eventData)
{
    Console.WriteLine($"Entity created: {eventData.EntityName}");
}

private void OnCollisionEnter(CollisionEnterEvent eventData)
{
    // Handle collision logic
    if (eventData.CollisionTypeA == "Player" && eventData.CollisionTypeB == "Item")
    {
        // Handle item pickup
    }
}
```

### 3. Fire Events

```csharp
// Queue events for processing
var healthEvent = ECSEventIntegration.CreateHealthChangedEvent(
    entityId, previousHealth, currentHealth, maxHealth, "Damage taken");
subscriptions.QueueEvent(healthEvent);

// Fire events immediately (bypasses queue)
var inputEvent = new InputActionEvent("PlayerAttack", playerId);
eventSystem.FireEventImmediate(inputEvent);
```

### 4. Process Events in Game Loop

```csharp
public void Update(GameTime gameTime)
{
    // Process events during update
    eventSystem.Update(gameTime);
    
    // Your other update logic...
}
```

## Available Events

### Entity Lifecycle Events
- `EntityCreatedEvent` - When entities are created
- `EntityDestroyedEvent` - When entities are destroyed
- `ComponentAddedEvent` - When components are added to entities
- `ComponentRemovedEvent` - When components are removed from entities

### Collision Events
- `CollisionEnterEvent` - When entities start colliding
- `CollisionExitEvent` - When entities stop colliding

### Health and Combat Events
- `HealthChangedEvent` - When entity health changes
- `EntityDeathEvent` - When entities die
- `DamageDealtEvent` - When damage is dealt
- `HealingReceivedEvent` - When healing is received

### Turn-Based Events
- `TurnStartedEvent` - When an entity's turn begins
- `TurnEndedEvent` - When an entity's turn ends
- `RoundStartedEvent` - When a new round begins
- `RoundEndedEvent` - When a round ends

### Ability Events
- `AbilityUsedEvent` - When abilities are used
- `ManaChangedEvent` - When mana changes

### Movement Events
- `EntityMovedEvent` - When entities move

### Input Events
- `InputActionEvent` - For input actions

## Integration with ECS Systems

### In Your ECS Systems

```csharp
public class YourECSSystem : ISystem
{
    private readonly EntityManager _entityManager;
    private readonly SystemEventSubscriptions _eventSubscriptions;

    public YourECSSystem(EntityManager entityManager, EventSystem eventSystem)
    {
        _entityManager = entityManager;
        _eventSubscriptions = new SystemEventSubscriptions(eventSystem);
        
        // Subscribe to relevant events
        _eventSubscriptions.Subscribe<CollisionEnterEvent>(OnCollisionEnter);
        _eventSubscriptions.Subscribe<HealthChangedEvent>(OnHealthChanged);
    }

    public void Initialize()
    {
        // System initialization
    }

    public void Update(GameTime gameTime)
    {
        // Your system logic
        
        // Fire events when needed
        var moveEvent = ECSEventIntegration.CreateEntityMovedEvent(
            entityId, oldPosition, newPosition, velocity);
        _eventSubscriptions.QueueEvent(moveEvent);
    }

    private void OnCollisionEnter(CollisionEnterEvent eventData)
    {
        // Handle collision in your system
    }

    // Don't forget to dispose!
    public void Dispose()
    {
        _eventSubscriptions?.Dispose();
    }
}
```

### In Your World Class

```csharp
public class World
{
    private readonly EntityManager _entityManager;
    private readonly EventSystem _eventSystem;
    private readonly List<ISystem> _systems;

    public World()
    {
        _entityManager = new EntityManager();
        _eventSystem = new EventSystem();
        _systems = new List<ISystem>();
    }

    public void AddSystem(ISystem system)
    {
        _systems.Add(system);
        system.Initialize();
    }

    public void Update(GameTime gameTime)
    {
        _entityManager.Update();
        
        // Update all systems
        foreach (var system in _systems)
        {
            system.Update(gameTime);
        }
        
        // Process events after all systems have updated
        _eventSystem.Update(gameTime);
    }
}
```

## Creating Custom Events

```csharp
public class CustomGameEvent : BaseEvent
{
    public string Message { get; }
    public Guid PlayerId { get; }

    public CustomGameEvent(string message, Guid playerId)
        : base(priority: 1, source: "CustomSystem")
    {
        Message = message;
        PlayerId = playerId;
    }
}

// Subscribe and use
subscriptions.Subscribe<CustomGameEvent>(OnCustomEvent);
var customEvent = new CustomGameEvent("Hello!", playerId);
subscriptions.QueueEvent(customEvent);
```

## Best Practices

1. **Use SystemEventSubscriptions**: Always use the subscription manager for proper cleanup
2. **Process Events in Update**: Call `eventSystem.Update()` in your main game loop
3. **Limit Events Per Frame**: Use the `maxEventsPerFrame` parameter to prevent frame drops
4. **Dispose Properly**: Always dispose of subscription managers when systems are destroyed
5. **Use Appropriate Priorities**: Set event priorities based on importance
6. **Batch Similar Events**: Use EventAggregator for batching similar events
7. **Handle Exceptions**: Event handlers should be robust and handle exceptions gracefully

## Performance Considerations

- Events are processed with a configurable limit per frame to maintain performance
- Thread-safe operations use minimal locking
- Old events are automatically cleaned up to prevent memory leaks
- Priority-based processing ensures important events are handled first
- Subscription lookup is optimized for fast event dispatching

## Thread Safety

The event system is designed to be thread-safe:
- EventQueue uses locking for thread-safe operations
- EventManager protects subscription lists during modification
- Events can be queued from any thread safely
- Processing should only occur on the main game thread
