using System;
using System.Collections.Generic;
using System.Linq;

namespace Monogame.Trigger.Core
{
    /// <summary>
    /// Thread-safe event queue that manages queued events and processes them in priority order
    /// </summary>
    public class EventQueue
    {
        private readonly List<IEvent> _events;
        private readonly object _lockObject;
        private readonly int _maxQueueSize;

        public int Count 
        { 
            get 
            { 
                lock (_lockObject) 
                { 
                    return _events.Count; 
                } 
            } 
        }

        public bool IsEmpty => Count == 0;
        public bool IsFull => Count >= _maxQueueSize;

        public EventQueue(int maxQueueSize = 1000)
        {
            _events = new List<IEvent>();
            _lockObject = new object();
            _maxQueueSize = maxQueueSize;
        }

        /// <summary>
        /// Enqueue an event for processing
        /// </summary>
        /// <param name="eventToQueue">The event to add to the queue</param>
        /// <returns>True if successfully queued, false if queue is full</returns>
        public bool Enqueue(IEvent eventToQueue)
        {
            if (eventToQueue == null)
                return false;

            lock (_lockObject)
            {
                if (_events.Count >= _maxQueueSize)
                {
                    // Queue is full, optionally remove oldest low-priority event
                    var oldestLowPriority = _events
                        .Where(e => e.Priority <= 0)
                        .OrderBy(e => e.Timestamp)
                        .FirstOrDefault();

                    if (oldestLowPriority != null)
                    {
                        _events.Remove(oldestLowPriority);
                    }
                    else
                    {
                        return false; // Queue full with high-priority events
                    }
                }

                _events.Add(eventToQueue);
                return true;
            }
        }

        /// <summary>
        /// Dequeue the highest priority event
        /// </summary>
        /// <returns>The highest priority event, or null if queue is empty</returns>
        public IEvent Dequeue()
        {
            lock (_lockObject)
            {
                if (_events.Count == 0)
                    return null;

                // Sort by priority (descending) then by timestamp (ascending)
                var nextEvent = _events
                    .OrderByDescending(e => e.Priority)
                    .ThenBy(e => e.Timestamp)
                    .First();

                _events.Remove(nextEvent);
                return nextEvent;
            }
        }

        /// <summary>
        /// Peek at the next event without removing it
        /// </summary>
        /// <returns>The next event to be processed, or null if queue is empty</returns>
        public IEvent Peek()
        {
            lock (_lockObject)
            {
                if (_events.Count == 0)
                    return null;

                return _events
                    .OrderByDescending(e => e.Priority)
                    .ThenBy(e => e.Timestamp)
                    .First();
            }
        }

        /// <summary>
        /// Get all events of a specific type without removing them
        /// </summary>
        /// <typeparam name="T">The event type to search for</typeparam>
        /// <returns>List of events of the specified type</returns>
        public List<T> GetEventsOfType<T>() where T : class, IEvent
        {
            lock (_lockObject)
            {
                return _events.OfType<T>().ToList();
            }
        }

        /// <summary>
        /// Remove all events of a specific type
        /// </summary>
        /// <typeparam name="T">The event type to remove</typeparam>
        /// <returns>Number of events removed</returns>
        public int RemoveEventsOfType<T>() where T : class, IEvent
        {
            lock (_lockObject)
            {
                var eventsToRemove = _events.OfType<T>().ToList();
                foreach (var eventToRemove in eventsToRemove)
                {
                    _events.Remove(eventToRemove);
                }
                return eventsToRemove.Count;
            }
        }

        /// <summary>
        /// Clear all events from the queue
        /// </summary>
        public void Clear()
        {
            lock (_lockObject)
            {
                _events.Clear();
            }
        }

        /// <summary>
        /// Get a snapshot of all events in the queue (for debugging)
        /// </summary>
        /// <returns>Array of all events currently in the queue</returns>
        public IEvent[] GetSnapshot()
        {
            lock (_lockObject)
            {
                return _events.ToArray();
            }
        }

        /// <summary>
        /// Remove events older than the specified age
        /// </summary>
        /// <param name="maxAge">Maximum age of events to keep</param>
        /// <returns>Number of events removed</returns>
        public int RemoveOldEvents(TimeSpan maxAge)
        {
            var cutoffTime = DateTime.UtcNow - maxAge;
            
            lock (_lockObject)
            {
                var oldEvents = _events.Where(e => e.Timestamp < cutoffTime).ToList();
                foreach (var oldEvent in oldEvents)
                {
                    _events.Remove(oldEvent);
                }
                return oldEvents.Count;
            }
        }
    }
}
