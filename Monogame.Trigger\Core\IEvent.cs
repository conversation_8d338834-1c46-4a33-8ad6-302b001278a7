using System;

namespace Monogame.Trigger.Core
{
    /// <summary>
    /// Base interface that all events must implement
    /// </summary>
    public interface IEvent
    {
        /// <summary>
        /// Unique identifier for this event instance
        /// </summary>
        Guid EventId { get; }

        /// <summary>
        /// Timestamp when the event was created
        /// </summary>
        DateTime Timestamp { get; }

        /// <summary>
        /// Priority level for event processing (higher values processed first)
        /// </summary>
        int Priority { get; }

        /// <summary>
        /// Whether this event should be processed immediately or queued
        /// </summary>
        bool IsImmediate { get; }

        /// <summary>
        /// Optional source identifier for tracking event origin
        /// </summary>
        string Source { get; }
    }

    /// <summary>
    /// Base abstract class for events providing common functionality
    /// </summary>
    public abstract class BaseEvent : IEvent
    {
        public Guid EventId { get; }
        public DateTime Timestamp { get; }
        public virtual int Priority { get; }
        public virtual bool IsImmediate { get; }
        public virtual string Source { get; }

        protected BaseEvent()
        {
            EventId = Guid.NewGuid();
            Timestamp = DateTime.UtcNow;
            Priority = 0;
            IsImmediate = false;
            Source = string.Empty;
        }

        protected BaseEvent(int priority, bool isImmediate = false, string source = "")
        {
            EventId = Guid.NewGuid();
            Timestamp = DateTime.UtcNow;
            Priority = priority;
            IsImmediate = isImmediate;
            Source = source ?? string.Empty;
        }
    }
}