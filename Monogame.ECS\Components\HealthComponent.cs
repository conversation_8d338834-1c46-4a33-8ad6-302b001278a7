using System;
using Monogame.ECS.Core.BaseClasses;

namespace Monogame.ECS.Components
{
    public class HealthComponent : Component
    {
        public int CurrentHealth { get; set; }
        public int MaxHealth { get; set; }
        public bool IsAlive => CurrentHealth > 0;
        public float HealthPercentage => MaxHealth > 0 ? (float)CurrentHealth / MaxHealth : 0f;

        public HealthComponent()
        {
            CurrentHealth = 100;
            MaxHealth = 100;
        }

        public HealthComponent(int maxHealth) : this()
        {
            MaxHealth = maxHealth;
            CurrentHealth = maxHealth;
        }

        public void TakeDamage(int damage)
        {
            CurrentHealth = Math.Max(0, CurrentHealth - damage);
        }

        public void Heal(int amount)
        {
            CurrentHealth = Math.Min(MaxHealth, CurrentHealth + amount);
        }

        public void FullHeal()
        {
            CurrentHealth = MaxHealth;
        }

        public void SetMaxHealth(int newMaxHealth, bool healToFull = false)
        {
            MaxHealth = newMaxHealth;
            if (healToFull)
            {
                CurrentHealth = MaxHealth;
            }
            else
            {
                CurrentHealth = Math.Min(<PERSON>Health, MaxHealth);
            }
        }
    }
}
