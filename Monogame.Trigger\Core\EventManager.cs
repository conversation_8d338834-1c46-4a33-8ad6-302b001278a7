using System;
using System.Collections.Generic;
using System.Linq;

namespace Monogame.Trigger.Core
{
    /// <summary>
    /// Central event manager that handles event subscription, unsubscription, and dispatching
    /// </summary>
    public class EventManager
    {
        private readonly Dictionary<Type, List<IEventSubscription>> _subscriptions;
        private readonly EventQueue _eventQueue;
        private readonly object _lockObject;
        private bool _isProcessing;

        public int QueuedEventCount => _eventQueue.Count;
        public int SubscriptionCount => _subscriptions.Values.Sum(list => list.Count);

        public EventManager(int maxQueueSize = 1000)
        {
            _subscriptions = new Dictionary<Type, List<IEventSubscription>>();
            _eventQueue = new EventQueue(maxQueueSize);
            _lockObject = new object();
            _isProcessing = false;
        }

        /// <summary>
        /// Subscribe to events of a specific type
        /// </summary>
        /// <typeparam name="T">The event type to subscribe to</typeparam>
        /// <param name="handler">The callback to execute when the event is triggered</param>
        /// <returns>Subscription handle for unsubscribing</returns>
        public IEventSubscription Subscribe<T>(Action<T> handler) where T : class, IEvent
        {
            if (handler == null)
                throw new ArgumentNullException(nameof(handler));

            var eventType = typeof(T);
            var subscription = new EventSubscription<T>(handler, this);

            lock (_lockObject)
            {
                if (!_subscriptions.ContainsKey(eventType))
                {
                    _subscriptions[eventType] = new List<IEventSubscription>();
                }

                _subscriptions[eventType].Add(subscription);
            }

            return subscription;
        }

        /// <summary>
        /// Unsubscribe from events
        /// </summary>
        /// <param name="subscription">The subscription to remove</param>
        public void Unsubscribe(IEventSubscription subscription)
        {
            if (subscription == null)
                return;

            lock (_lockObject)
            {
                var eventType = subscription.EventType;
                if (_subscriptions.ContainsKey(eventType))
                {
                    _subscriptions[eventType].Remove(subscription);
                    
                    // Clean up empty subscription lists
                    if (_subscriptions[eventType].Count == 0)
                    {
                        _subscriptions.Remove(eventType);
                    }
                }
            }
        }

        /// <summary>
        /// Queue an event for processing
        /// </summary>
        /// <param name="eventToQueue">The event to queue</param>
        /// <returns>True if successfully queued</returns>
        public bool QueueEvent(IEvent eventToQueue)
        {
            if (eventToQueue == null)
                return false;

            if (eventToQueue.IsImmediate)
            {
                DispatchEventImmediate(eventToQueue);
                return true;
            }

            return _eventQueue.Enqueue(eventToQueue);
        }

        /// <summary>
        /// Process all queued events
        /// </summary>
        /// <param name="maxEventsToProcess">Maximum number of events to process in this call (0 = all)</param>
        /// <returns>Number of events processed</returns>
        public int ProcessEvents(int maxEventsToProcess = 0)
        {
            if (_isProcessing)
                return 0; // Prevent recursive processing

            _isProcessing = true;
            int eventsProcessed = 0;

            try
            {
                while (!_eventQueue.IsEmpty && (maxEventsToProcess == 0 || eventsProcessed < maxEventsToProcess))
                {
                    var eventToProcess = _eventQueue.Dequeue();
                    if (eventToProcess != null)
                    {
                        DispatchEventImmediate(eventToProcess);
                        eventsProcessed++;
                    }
                }
            }
            finally
            {
                _isProcessing = false;
            }

            return eventsProcessed;
        }

        /// <summary>
        /// Dispatch an event immediately to all subscribers
        /// </summary>
        /// <param name="eventToDispatch">The event to dispatch</param>
        private void DispatchEventImmediate(IEvent eventToDispatch)
        {
            if (eventToDispatch == null)
                return;

            var eventType = eventToDispatch.GetType();
            List<IEventSubscription> subscribers = null;

            lock (_lockObject)
            {
                // Get all subscribers for this event type and its base types
                var allSubscribers = new List<IEventSubscription>();
                
                foreach (var kvp in _subscriptions)
                {
                    if (kvp.Key.IsAssignableFrom(eventType))
                    {
                        allSubscribers.AddRange(kvp.Value);
                    }
                }

                subscribers = allSubscribers.ToList(); // Create a copy to avoid lock during dispatch
            }

            // Dispatch to all subscribers outside of lock
            foreach (var subscription in subscribers)
            {
                try
                {
                    subscription.Invoke(eventToDispatch);
                }
                catch (Exception ex)
                {
                    // Log the exception but continue processing other subscribers
                    // In a real implementation, you'd want proper logging here
                    Console.WriteLine($"Error dispatching event {eventType.Name}: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Get the number of subscribers for a specific event type
        /// </summary>
        /// <typeparam name="T">The event type</typeparam>
        /// <returns>Number of subscribers</returns>
        public int GetSubscriberCount<T>() where T : class, IEvent
        {
            var eventType = typeof(T);
            lock (_lockObject)
            {
                return _subscriptions.ContainsKey(eventType) ? _subscriptions[eventType].Count : 0;
            }
        }

        /// <summary>
        /// Clear all subscriptions
        /// </summary>
        public void ClearSubscriptions()
        {
            lock (_lockObject)
            {
                _subscriptions.Clear();
            }
        }

        /// <summary>
        /// Clear all queued events
        /// </summary>
        public void ClearQueue()
        {
            _eventQueue.Clear();
        }

        /// <summary>
        /// Clear both subscriptions and queued events
        /// </summary>
        public void Clear()
        {
            ClearSubscriptions();
            ClearQueue();
        }

        /// <summary>
        /// Remove old events from the queue
        /// </summary>
        /// <param name="maxAge">Maximum age of events to keep</param>
        /// <returns>Number of events removed</returns>
        public int CleanupOldEvents(TimeSpan maxAge)
        {
            return _eventQueue.RemoveOldEvents(maxAge);
        }
    }
}
