using System;
using System.Collections.Generic;
using Microsoft.Xna.Framework;
using Monogame.ECS.Core;
using Monogame.ECS.Core.BaseClasses;
using Monogame.ECS.Components;

namespace Monogame.ECS.System
{
    public class AnimationSystem : ISystem
    {
        private readonly EntityManager _entityManager;

        public IEnumerable<Type> RequiredComponentTypes => new[]
        {
            typeof(AnimationComponent)
        };

        public AnimationSystem(EntityManager entityManager)
        {
            _entityManager = entityManager;
        }

        public void Initialize()
        {
            // No initialization needed
        }

        public void Update(GameTime gameTime)
        {
            var animatedEntities = _entityManager.GetEntitiesWithComponent<AnimationComponent>();

            foreach (var entity in animatedEntities)
            {
                var animation = entity.GetComponent<AnimationComponent>();
                var movement = entity.GetComponent<MovementComponent>();
                var sprite = entity.GetComponent<SpriteComponent>();

                // Update animation timing
                animation.Update(gameTime);

                // Auto-select animations based on movement state
                if (movement != null)
                {
                    UpdateMovementAnimations(animation, movement);
                }

                // Update sprite source rectangle if animation is active
                if (sprite != null && animation.CurrentFrameRectangle.HasValue)
                {
                    sprite.SourceRectangle = animation.CurrentFrameRectangle.Value;
                }
            }
        }

        private void UpdateMovementAnimations(AnimationComponent animation, MovementComponent movement)
        {
            // Auto-switch between idle and walking animations based on movement
            if (movement.IsMoving)
            {
                // Try to play walking animation if not already playing
                if (animation.Animations.ContainsKey("walk") && 
                    animation.CurrentAnimationName != "walk")
                {
                    animation.PlayAnimation("walk");
                }
                else if (animation.Animations.ContainsKey("run") && 
                         movement.Speed > 150f && 
                         animation.CurrentAnimationName != "run")
                {
                    animation.PlayAnimation("run");
                }
            }
            else
            {
                // Play idle animation when not moving
                if (animation.Animations.ContainsKey("idle") && 
                    animation.CurrentAnimationName != "idle")
                {
                    animation.PlayAnimation("idle");
                }
            }
        }

        public void PlayAnimation(Entity entity, string animationName, bool restart = false)
        {
            var animation = entity.GetComponent<AnimationComponent>();
            animation?.PlayAnimation(animationName, restart);
        }

        public void StopAnimation(Entity entity)
        {
            var animation = entity.GetComponent<AnimationComponent>();
            animation?.StopAnimation();
        }

        public void PauseAnimation(Entity entity)
        {
            var animation = entity.GetComponent<AnimationComponent>();
            animation?.PauseAnimation();
        }

        public void ResumeAnimation(Entity entity)
        {
            var animation = entity.GetComponent<AnimationComponent>();
            animation?.ResumeAnimation();
        }

        public bool IsAnimationFinished(Entity entity)
        {
            var animation = entity.GetComponent<AnimationComponent>();
            return animation?.IsFinished ?? true;
        }

        public bool IsAnimationPlaying(Entity entity, string animationName)
        {
            var animation = entity.GetComponent<AnimationComponent>();
            return animation != null && 
                   animation.CurrentAnimationName == animationName && 
                   animation.IsPlaying;
        }

        public void AddAnimation(Entity entity, Animation newAnimation)
        {
            var animation = entity.GetComponent<AnimationComponent>();
            animation?.AddAnimation(newAnimation);
        }

        // Helper method to create common animations
        public static Animation CreateWalkAnimation(List<Rectangle> frames, float frameDuration = 0.1f)
        {
            return new Animation("walk", frames, frameDuration, true);
        }

        public static Animation CreateIdleAnimation(List<Rectangle> frames, float frameDuration = 0.2f)
        {
            return new Animation("idle", frames, frameDuration, true);
        }

        public static Animation CreateAttackAnimation(List<Rectangle> frames, float frameDuration = 0.08f)
        {
            return new Animation("attack", frames, frameDuration, false);
        }

        public static Animation CreateDeathAnimation(List<Rectangle> frames, float frameDuration = 0.15f)
        {
            return new Animation("death", frames, frameDuration, false);
        }
    }
}
