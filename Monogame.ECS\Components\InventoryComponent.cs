using System;
using System.Collections.Generic;
using System.Linq;
using Monogame.ECS.Core.BaseClasses;

namespace Monogame.ECS.Components
{
    public class InventoryItem
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public int Quantity { get; set; }
        public int MaxStackSize { get; set; }
        public bool IsStackable => MaxStackSize > 1;

        public InventoryItem(string id, string name, int quantity = 1, int maxStackSize = 1)
        {
            Id = id;
            Name = name;
            Quantity = quantity;
            MaxStackSize = maxStackSize;
            Description = "";
        }
    }

    public class InventorySlot
    {
        public InventoryItem Item { get; set; }
        public bool IsEmpty => Item == null || Item.Quantity <= 0;

        public InventorySlot()
        {
            Item = null;
        }

        public InventorySlot(InventoryItem item)
        {
            Item = item;
        }
    }

    public class InventoryComponent : Component
    {
        public List<InventorySlot> Slots { get; set; }
        public int MaxSlots { get; set; }
        public int UsedSlots => Slots.Count(slot => !slot.IsEmpty);
        public int FreeSlots => MaxSlots - UsedSlots;
        public bool IsFull => UsedSlots >= MaxSlots;

        public InventoryComponent(int maxSlots = 20)
        {
            MaxSlots = maxSlots;
            Slots = new List<InventorySlot>();
            
            // Initialize empty slots
            for (int i = 0; i < maxSlots; i++)
            {
                Slots.Add(new InventorySlot());
            }
        }

        public bool AddItem(InventoryItem item)
        {
            if (item == null || item.Quantity <= 0)
                return false;

            // Try to stack with existing items first
            if (item.IsStackable)
            {
                foreach (var slot in Slots)
                {
                    if (!slot.IsEmpty && slot.Item.Id == item.Id)
                    {
                        int spaceInStack = slot.Item.MaxStackSize - slot.Item.Quantity;
                        if (spaceInStack > 0)
                        {
                            int amountToAdd = Math.Min(spaceInStack, item.Quantity);
                            slot.Item.Quantity += amountToAdd;
                            item.Quantity -= amountToAdd;

                            if (item.Quantity <= 0)
                                return true;
                        }
                    }
                }
            }

            // Find empty slot for remaining quantity
            var emptySlot = Slots.FirstOrDefault(slot => slot.IsEmpty);
            if (emptySlot != null && item.Quantity > 0)
            {
                emptySlot.Item = new InventoryItem(item.Id, item.Name, item.Quantity, item.MaxStackSize)
                {
                    Description = item.Description
                };
                return true;
            }

            return false; // Inventory full
        }

        public bool RemoveItem(string itemId, int quantity = 1)
        {
            int remainingToRemove = quantity;

            foreach (var slot in Slots)
            {
                if (!slot.IsEmpty && slot.Item.Id == itemId)
                {
                    int amountToRemove = Math.Min(slot.Item.Quantity, remainingToRemove);
                    slot.Item.Quantity -= amountToRemove;
                    remainingToRemove -= amountToRemove;

                    if (slot.Item.Quantity <= 0)
                    {
                        slot.Item = null;
                    }

                    if (remainingToRemove <= 0)
                        return true;
                }
            }

            return remainingToRemove <= 0;
        }

        public int GetItemCount(string itemId)
        {
            return Slots.Where(slot => !slot.IsEmpty && slot.Item.Id == itemId)
                       .Sum(slot => slot.Item.Quantity);
        }

        public bool HasItem(string itemId, int quantity = 1)
        {
            return GetItemCount(itemId) >= quantity;
        }

        public InventoryItem GetItem(int slotIndex)
        {
            if (slotIndex >= 0 && slotIndex < Slots.Count)
            {
                return Slots[slotIndex].Item;
            }
            return null;
        }

        public List<InventoryItem> GetAllItems()
        {
            return Slots.Where(slot => !slot.IsEmpty)
                       .Select(slot => slot.Item)
                       .ToList();
        }

        public void Clear()
        {
            foreach (var slot in Slots)
            {
                slot.Item = null;
            }
        }
    }
}
