﻿using Microsoft.Xna.Framework;
using Monogame.ECS.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Monogame.Scene.Core
{
    public interface IScene
    {
        World World { get; set; }
        void Initialize();
        void Update(GameTime gameTime);
        void Draw(GameTime gameTime);
        void Exit();
    }
}
