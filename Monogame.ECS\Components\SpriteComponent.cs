﻿using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;
using Monogame.ECS.Core.BaseClasses;

namespace Monogame.ECS.Components
{
    public class SpriteComponent : Component
    {
        public Texture2D Texture { get; set; }
        public Rectangle? SourceRectangle { get; set; }
        public Color Color { get; set; }
        public SpriteEffects Effects { get; set; }
        public float LayerDepth { get; set; }
        public bool IsVisible { get; set; }

        public SpriteComponent()
        {
            Color = Color.White;
            Effects = SpriteEffects.None;
            LayerDepth = 0f;
            IsVisible = true;
        }

        public SpriteComponent(Texture2D texture) : this()
        {
            Texture = texture;
        }

        public SpriteComponent(Texture2D texture, Rectangle sourceRectangle) : this(texture)
        {
            SourceRectangle = sourceRectangle;
        }
    }
}
