﻿// File: World.cs
using Microsoft.Xna.Framework;
using Monogame.ECS.Core.BaseClasses;
using Monogame.ECS.Core.Factories;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Monogame.ECS.Core
{
    public class World
    {
        private readonly EntityManager _entityManager;
        private readonly List<ISystem> _systems;
        private readonly List<Type> _registeredSystemTypes;
        private readonly Dictionary<string, IEntityFactory> _entityFactories;

        public World()
        {
            _entityManager = new EntityManager();
            _systems = new List<ISystem>();
            _registeredSystemTypes = new List<Type>();
            _entityFactories = new Dictionary<string, IEntityFactory>();
        }

        public EntityManager EntityManager => _entityManager;

        // Register a system manually
        public void AddSystem(ISystem system)
        {
            if (!_registeredSystemTypes.Contains(system.GetType()))
            {
                _systems.Add(system);
                _registeredSystemTypes.Add(system.GetType());
                system.Initialize();
            }
        }

        // Automatically register systems that match available components
        public void AutoRegisterSystems(IEnumerable<Type> systemTypes)
        {
            foreach (var systemType in systemTypes)
            {
                if (!typeof(ISystem).IsAssignableFrom(systemType) || systemType.IsAbstract || systemType.IsInterface)
                    continue;

                if (_registeredSystemTypes.Contains(systemType))
                    continue;

                var system = (ISystem)Activator.CreateInstance(systemType, _entityManager);

                // Check if any entities already match this system's requirements
                var matchingEntities = _entityManager.GetEntities()
                    .Where(e => system.RequiredComponentTypes.All(t => e.HasComponent(t)));

                if (matchingEntities.Any())
                {
                    AddSystem(system);
                }
            }
        }

        // Register entity factories
        public void RegisterFactory(IEntityFactory factory)
        {
            _entityFactories[factory.FactoryName] = factory;
        }

        public void RegisterFactories(IEnumerable<IEntityFactory> factories)
        {
            foreach (var factory in factories)
            {
                RegisterFactory(factory);
            }
        }

        // Create entity from factory
        public Entity CreateEntity(string factoryName)
        {
            if (_entityFactories.TryGetValue(factoryName, out var factory))
            {
                var entity = factory.CreateEntity();
                _entityManager.AddEntity(entity);
                return entity;
            }

            throw new ArgumentException($"Factory '{factoryName}' not found.");
        }

        // Create entity and immediately return its ID
        public Guid CreateEntityAndGetId(string factoryName)
        {
            var entity = CreateEntity(factoryName);
            return entity.Id;
        }

        // Get all registered factory names
        public IEnumerable<string> GetAvailableFactoryNames()
        {
            return _entityFactories.Keys;
        }

        public void Update(GameTime gameTime)
        {
            _entityManager.Update();

            foreach (var system in _systems)
            {
                system.Update(gameTime);
            }
        }
    }
}