﻿// File: World.cs
using Microsoft.Xna.Framework;
using Monogame.ECS.Core.BaseClasses;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Monogame.ECS.Core
{
    public class World
    {
        private readonly EntityManager _entityManager;
        private readonly List<ISystem> _systems;
        private readonly List<Type> _registeredSystemTypes;

        public World()
        {
            _entityManager = new EntityManager();
            _systems = new List<ISystem>();
            _registeredSystemTypes = new List<Type>();
        }

        public EntityManager EntityManager => _entityManager;

        // Register a system manually
        public void AddSystem(ISystem system)
        {
            if (!_registeredSystemTypes.Contains(system.GetType()))
            {
                _systems.Add(system);
                _registeredSystemTypes.Add(system.GetType());
                system.Initialize();
            }
        }

        // Automatically register systems that match available components
        public void AutoRegisterSystems(IEnumerable<Type> systemTypes)
        {
            foreach (var systemType in systemTypes)
            {
                if (!typeof(ISystem).IsAssignableFrom(systemType) || systemType.IsAbstract || systemType.IsInterface)
                    continue;

                if (_registeredSystemTypes.Contains(systemType))
                    continue;

                var system = (ISystem)Activator.CreateInstance(systemType, _entityManager);

                // Check if any entities already match this system's requirements
                var matchingEntities = _entityManager.GetEntities()
                    .Where(e => system.RequiredComponentTypes.All(t => e.HasComponent(t)));

                if (matchingEntities.Any())
                {
                    AddSystem(system);
                }
            }
        }

        public void Update(GameTime gameTime)
        {
            _entityManager.Update();

            foreach (var system in _systems)
            {
                system.Update(gameTime);
            }
        }
    }
}