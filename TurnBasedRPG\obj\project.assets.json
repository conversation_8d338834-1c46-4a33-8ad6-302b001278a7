{"version": 3, "targets": {"net8.0": {"MonoGame.Content.Builder.Task/3.8.2.1105": {"type": "package", "build": {"build/MonoGame.Content.Builder.Task.props": {}, "build/MonoGame.Content.Builder.Task.targets": {}}}, "MonoGame.Framework.DesktopGL/3.8.2.1105": {"type": "package", "dependencies": {"MonoGame.Library.SDL": "********", "NVorbis": "0.10.4"}, "compile": {"lib/net8.0/MonoGame.Framework.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/MonoGame.Framework.dll": {"related": ".xml"}}, "build": {"build/MonoGame.Framework.DesktopGL.targets": {}}, "runtimeTargets": {"runtimes/linux-x64/native/libopenal.so.1": {"assetType": "native", "rid": "linux-x64"}, "runtimes/osx/native/libopenal.1.dylib": {"assetType": "native", "rid": "osx"}, "runtimes/win-x64/native/soft_oal.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/soft_oal.dll": {"assetType": "native", "rid": "win-x86"}}}, "MonoGame.Library.SDL/********": {"type": "package", "runtimeTargets": {"runtimes/linux-x64/native/libSDL2-2.0.so.0": {"assetType": "native", "rid": "linux-x64"}, "runtimes/osx/native/libSDL2.dylib": {"assetType": "native", "rid": "osx"}, "runtimes/win-x64/native/SDL2.dll": {"assetType": "native", "rid": "win-x64"}}}, "NVorbis/0.10.4": {"type": "package", "dependencies": {"System.Memory": "4.5.3", "System.ValueTuple": "4.5.0"}, "compile": {"lib/netstandard2.0/NVorbis.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/NVorbis.dll": {"related": ".xml"}}}, "System.Memory/4.5.3": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.ValueTuple/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "Monogame.ECS/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"MonoGame.Framework.DesktopGL": "3.8.2.1105"}, "compile": {"bin/placeholder/Monogame.ECS.dll": {}}, "runtime": {"bin/placeholder/Monogame.ECS.dll": {}}}, "Monogame.Scene/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"MonoGame.Framework.DesktopGL": "3.8.2.1105", "Monogame.ECS": "1.0.0"}, "compile": {"bin/placeholder/Monogame.Scene.dll": {}}, "runtime": {"bin/placeholder/Monogame.Scene.dll": {}}}, "Monogame.Trigger/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"MonoGame.Framework.DesktopGL": "3.8.2.1105", "Monogame.ECS": "1.0.0"}, "compile": {"bin/placeholder/Monogame.Trigger.dll": {}}, "runtime": {"bin/placeholder/Monogame.Trigger.dll": {}}}}}, "libraries": {"MonoGame.Content.Builder.Task/3.8.2.1105": {"sha512": "wNlkLaITOqMs9MmCYLp3Qc7QfFng3FhIq/b+njdYV3cTVpLYgs/i56FYtJjm8jLoUA/2sJ36Qo/uqWpgW+jBuQ==", "type": "package", "path": "monogame.content.builder.task/3.8.2.1105", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/MonoGame.Content.Builder.Task.props", "build/MonoGame.Content.Builder.Task.targets", "monogame.content.builder.task.3.8.2.1105.nupkg.sha512", "monogame.content.builder.task.nuspec"]}, "MonoGame.Framework.DesktopGL/3.8.2.1105": {"sha512": "QwjhmpyMZ9js4zqoYN/0cz//nYZ1u5yB8KWJ1P7nbViqfUyzuFDvGqgUEXFLp+n0Rx4z9PuKfwL5nQpp9LrXhg==", "type": "package", "path": "monogame.framework.desktopgl/3.8.2.1105", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/MonoGame.Framework.DesktopGL.targets", "lib/net8.0/MonoGame.Framework.dll", "lib/net8.0/MonoGame.Framework.xml", "monogame.framework.desktopgl.3.8.2.1105.nupkg.sha512", "monogame.framework.desktopgl.nuspec", "runtimes/linux-x64/native/libopenal.so.1", "runtimes/osx/native/libopenal.1.dylib", "runtimes/win-x64/native/soft_oal.dll", "runtimes/win-x86/native/soft_oal.dll"]}, "MonoGame.Library.SDL/********": {"sha512": "wSbYCCLeT3p3GZtU8GaYA5+XTjt4kM01yIvik3qm3sjU53jf/w0Pp0GBvOTCHgKxEGwb/IinHIASMcLuVnmgCA==", "type": "package", "path": "monogame.library.sdl/********", "files": [".nupkg.metadata", ".signature.p7s", "monogame.library.sdl.********.nupkg.sha512", "monogame.library.sdl.nuspec", "runtimes/linux-x64/native/libSDL2-2.0.so.0", "runtimes/osx/native/libSDL2.dylib", "runtimes/win-x64/native/SDL2.dll"]}, "NVorbis/0.10.4": {"sha512": "WYnil3DhQHzjCY0dM9I2B3r1vWip90AOuQd25KE4NrjPQBg0tBJFluRLm5YPnO5ZLDmwrfosY8jCQGQRmWI/Pg==", "type": "package", "path": "nvorbis/0.10.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "lib/net45/NVorbis.dll", "lib/net45/NVorbis.xml", "lib/netstandard2.0/NVorbis.dll", "lib/netstandard2.0/NVorbis.xml", "nvorbis.0.10.4.nupkg.sha512", "nvorbis.nuspec"]}, "System.Memory/4.5.3": {"sha512": "3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==", "type": "package", "path": "system.memory/4.5.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.3.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ValueTuple/4.5.0": {"sha512": "okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "type": "package", "path": "system.valuetuple/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.ValueTuple.dll", "lib/net461/System.ValueTuple.xml", "lib/net47/System.ValueTuple.dll", "lib/net47/System.ValueTuple.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.ValueTuple.dll", "lib/netstandard1.0/System.ValueTuple.xml", "lib/netstandard2.0/_._", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net461/System.ValueTuple.dll", "ref/net47/System.ValueTuple.dll", "ref/netcoreapp2.0/_._", "ref/netstandard2.0/_._", "ref/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.valuetuple.4.5.0.nupkg.sha512", "system.valuetuple.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Monogame.ECS/1.0.0": {"type": "project", "path": "../Monogame.ECS/Monogame.ECS.csproj", "msbuildProject": "../Monogame.ECS/Monogame.ECS.csproj"}, "Monogame.Scene/1.0.0": {"type": "project", "path": "../Monogame.Scene/Monogame.Scene.csproj", "msbuildProject": "../Monogame.Scene/Monogame.Scene.csproj"}, "Monogame.Trigger/1.0.0": {"type": "project", "path": "../Monogame.Trigger/Monogame.Trigger.csproj", "msbuildProject": "../Monogame.Trigger/Monogame.Trigger.csproj"}}, "projectFileDependencyGroups": {"net8.0": ["MonoGame.Content.Builder.Task >= 3.8.2.1105", "MonoGame.Framework.DesktopGL >= 3.8.2.1105", "Monogame.ECS >= 1.0.0", "Monogame.Scene >= 1.0.0", "Monogame.Trigger >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}, "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\TurnBasedRPG\\TurnBasedRPG.csproj", "projectName": "TurnBasedRPG", "projectPath": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\TurnBasedRPG\\TurnBasedRPG.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\TurnBasedRPG\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.ECS\\Monogame.ECS.csproj": {"projectPath": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.ECS\\Monogame.ECS.csproj"}, "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.Scene\\Monogame.Scene.csproj": {"projectPath": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.Scene\\Monogame.Scene.csproj"}, "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.Trigger\\Monogame.Trigger.csproj": {"projectPath": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.Trigger\\Monogame.Trigger.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"MonoGame.Content.Builder.Task": {"target": "Package", "version": "[3.8.2.1105, )"}, "MonoGame.Framework.DesktopGL": {"target": "Package", "version": "[3.8.2.1105, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}