{"Version": 1, "WorkspaceRootPath": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\TurnBasedRPG\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{ED816FA2-6C70-4536-B0ED-CC617C1ADBD4}|..\\Monogame.ECS\\Monogame.ECS.csproj|c:\\projects\\monogame\\turnbasedrpg\\turnbased_20250822\\monogame.ecs\\components\\statscomponent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{004308C2-E952-43AF-AFD1-14150939B838}|..\\Monogame.Scene\\Monogame.Scene.csproj|c:\\projects\\monogame\\turnbasedrpg\\turnbased_20250822\\monogame.scene\\core\\scenemanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{004308C2-E952-43AF-AFD1-14150939B838}|..\\Monogame.Scene\\Monogame.Scene.csproj|c:\\projects\\monogame\\turnbasedrpg\\turnbased_20250822\\monogame.scene\\core\\basescene.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{004308C2-E952-43AF-AFD1-14150939B838}|..\\Monogame.Scene\\Monogame.Scene.csproj|c:\\projects\\monogame\\turnbasedrpg\\turnbased_20250822\\monogame.scene\\core\\iscene.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{ED816FA2-6C70-4536-B0ED-CC617C1ADBD4}|..\\Monogame.ECS\\Monogame.ECS.csproj|c:\\projects\\monogame\\turnbasedrpg\\turnbased_20250822\\monogame.ecs\\core\\world.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{ED816FA2-6C70-4536-B0ED-CC617C1ADBD4}|..\\Monogame.ECS\\Monogame.ECS.csproj|c:\\projects\\monogame\\turnbasedrpg\\turnbased_20250822\\monogame.ecs\\core\\entitymanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{ED816FA2-6C70-4536-B0ED-CC617C1ADBD4}|..\\Monogame.ECS\\Monogame.ECS.csproj|c:\\projects\\monogame\\turnbasedrpg\\turnbased_20250822\\monogame.ecs\\core\\baseclasses\\entity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{ED816FA2-6C70-4536-B0ED-CC617C1ADBD4}|..\\Monogame.ECS\\Monogame.ECS.csproj|c:\\projects\\monogame\\turnbasedrpg\\turnbased_20250822\\monogame.ecs\\core\\factories\\baseentityfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{ED816FA2-6C70-4536-B0ED-CC617C1ADBD4}|..\\Monogame.ECS\\Monogame.ECS.csproj|c:\\projects\\monogame\\turnbasedrpg\\turnbased_20250822\\monogame.ecs\\core\\factories\\ientityfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{ED816FA2-6C70-4536-B0ED-CC617C1ADBD4}|..\\Monogame.ECS\\Monogame.ECS.csproj|c:\\projects\\monogame\\turnbasedrpg\\turnbased_20250822\\monogame.ecs\\core\\baseclasses\\system.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{ED816FA2-6C70-4536-B0ED-CC617C1ADBD4}|..\\Monogame.ECS\\Monogame.ECS.csproj|c:\\projects\\monogame\\turnbasedrpg\\turnbased_20250822\\monogame.ecs\\core\\baseclasses\\isystem.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{ED816FA2-6C70-4536-B0ED-CC617C1ADBD4}|..\\Monogame.ECS\\Monogame.ECS.csproj|c:\\projects\\monogame\\turnbasedrpg\\turnbased_20250822\\monogame.ecs\\core\\baseclasses\\component.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{ED816FA2-6C70-4536-B0ED-CC617C1ADBD4}|..\\Monogame.ECS\\Monogame.ECS.csproj|c:\\projects\\monogame\\turnbasedrpg\\turnbased_20250822\\monogame.ecs\\components\\transformcomponent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{ED816FA2-6C70-4536-B0ED-CC617C1ADBD4}|..\\Monogame.ECS\\Monogame.ECS.csproj|c:\\projects\\monogame\\turnbasedrpg\\turnbased_20250822\\monogame.ecs\\components\\spritecomponent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "StatsComponent.cs", "DocumentMoniker": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.ECS\\Components\\StatsComponent.cs", "RelativeDocumentMoniker": "..\\Monogame.ECS\\Components\\StatsComponent.cs", "ToolTip": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.ECS\\Components\\StatsComponent.cs", "RelativeToolTip": "..\\Monogame.ECS\\Components\\StatsComponent.cs", "ViewState": "AgIAAAkAAAAAAAAAAAA5wBEAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-25T17:52:50.697Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "BaseScene.cs", "DocumentMoniker": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.Scene\\Core\\BaseScene.cs", "RelativeDocumentMoniker": "..\\Monogame.Scene\\Core\\BaseScene.cs", "ToolTip": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.Scene\\Core\\BaseScene.cs", "RelativeToolTip": "..\\Monogame.Scene\\Core\\BaseScene.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-24T03:04:22.895Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "SceneManager.cs", "DocumentMoniker": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.Scene\\Core\\SceneManager.cs", "RelativeDocumentMoniker": "..\\Monogame.Scene\\Core\\SceneManager.cs", "ToolTip": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.Scene\\Core\\SceneManager.cs", "RelativeToolTip": "..\\Monogame.Scene\\Core\\SceneManager.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACMAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-24T03:03:15.04Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "IScene.cs", "DocumentMoniker": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.Scene\\Core\\IScene.cs", "RelativeDocumentMoniker": "..\\Monogame.Scene\\Core\\IScene.cs", "ToolTip": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.Scene\\Core\\IScene.cs", "RelativeToolTip": "..\\Monogame.Scene\\Core\\IScene.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-24T02:56:49.703Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "BaseEntityFactory.cs", "DocumentMoniker": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.ECS\\Core\\Factories\\BaseEntityFactory.cs", "RelativeDocumentMoniker": "..\\Monogame.ECS\\Core\\Factories\\BaseEntityFactory.cs", "ToolTip": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.ECS\\Core\\Factories\\BaseEntityFactory.cs", "RelativeToolTip": "..\\Monogame.ECS\\Core\\Factories\\BaseEntityFactory.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T03:42:52.073Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "IEntityFactory.cs", "DocumentMoniker": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.ECS\\Core\\Factories\\IEntityFactory.cs", "RelativeDocumentMoniker": "..\\Monogame.ECS\\Core\\Factories\\IEntityFactory.cs", "ToolTip": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.ECS\\Core\\Factories\\IEntityFactory.cs", "RelativeToolTip": "..\\Monogame.ECS\\Core\\Factories\\IEntityFactory.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T03:42:18.464Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "World.cs", "DocumentMoniker": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.ECS\\Core\\World.cs", "RelativeDocumentMoniker": "..\\Monogame.ECS\\Core\\World.cs", "ToolTip": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.ECS\\Core\\World.cs", "RelativeToolTip": "..\\Monogame.ECS\\Core\\World.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABgAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T03:41:07.539Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "ISystem.cs", "DocumentMoniker": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.ECS\\Core\\BaseClasses\\ISystem.cs", "RelativeDocumentMoniker": "..\\Monogame.ECS\\Core\\BaseClasses\\ISystem.cs", "ToolTip": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.ECS\\Core\\BaseClasses\\ISystem.cs", "RelativeToolTip": "..\\Monogame.ECS\\Core\\BaseClasses\\ISystem.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-23T03:39:37.957Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "SpriteComponent.cs", "DocumentMoniker": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.ECS\\Components\\SpriteComponent.cs", "RelativeDocumentMoniker": "..\\Monogame.ECS\\Components\\SpriteComponent.cs", "ToolTip": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.ECS\\Components\\SpriteComponent.cs", "RelativeToolTip": "..\\Monogame.ECS\\Components\\SpriteComponent.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-22T23:53:20.366Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "TransformComponent.cs", "DocumentMoniker": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.ECS\\Components\\TransformComponent.cs", "RelativeDocumentMoniker": "..\\Monogame.ECS\\Components\\TransformComponent.cs", "ToolTip": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.ECS\\Components\\TransformComponent.cs", "RelativeToolTip": "..\\Monogame.ECS\\Components\\TransformComponent.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-22T23:53:06.65Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "EntityManager.cs", "DocumentMoniker": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.ECS\\Core\\EntityManager.cs", "RelativeDocumentMoniker": "..\\Monogame.ECS\\Core\\EntityManager.cs", "ToolTip": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.ECS\\Core\\EntityManager.cs", "RelativeToolTip": "..\\Monogame.ECS\\Core\\EntityManager.cs", "ViewState": "AgIAABgAAAAAAAAAAAAowEEAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-22T23:51:52.868Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "System.cs", "DocumentMoniker": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.ECS\\Core\\BaseClasses\\System.cs", "RelativeDocumentMoniker": "..\\Monogame.ECS\\Core\\BaseClasses\\System.cs", "ToolTip": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.ECS\\Core\\BaseClasses\\System.cs", "RelativeToolTip": "..\\Monogame.ECS\\Core\\BaseClasses\\System.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-22T23:34:10.737Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "Component.cs", "DocumentMoniker": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.ECS\\Core\\BaseClasses\\Component.cs", "RelativeDocumentMoniker": "..\\Monogame.ECS\\Core\\BaseClasses\\Component.cs", "ToolTip": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.ECS\\Core\\BaseClasses\\Component.cs", "RelativeToolTip": "..\\Monogame.ECS\\Core\\BaseClasses\\Component.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-22T23:33:48.802Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "Entity.cs", "DocumentMoniker": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.ECS\\Core\\BaseClasses\\Entity.cs", "RelativeDocumentMoniker": "..\\Monogame.ECS\\Core\\BaseClasses\\Entity.cs", "ToolTip": "C:\\Projects\\MonoGame\\TurnBasedRPG\\TurnBased_20250822\\Monogame.ECS\\Core\\BaseClasses\\Entity.cs", "RelativeToolTip": "..\\Monogame.ECS\\Core\\BaseClasses\\Entity.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAD0AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-22T23:33:30.603Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:0:0:{cce594b6-0c39-4442-ba28-10c64ac7e89f}"}]}]}]}